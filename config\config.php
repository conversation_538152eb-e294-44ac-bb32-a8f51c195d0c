<?php
/**
 * Main Configuration File
 * websitedeveloper0002.in
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Site configuration
define('SITE_NAME', 'WebsiteDeveloper0002.in');
define('SITE_URL', 'http://localhost/websitedeveloper0002.in');
define('SITE_EMAIL', '<EMAIL>');
define('SITE_PHONE', '+91-9999999999');
define('WHATSAPP_NUMBER', '+91-9999999999');

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'websitedeveloper0002');
define('DB_USER', 'root');
define('DB_PASS', '');

// Security settings
define('ENCRYPTION_KEY', 'your-secret-encryption-key-here');
define('JWT_SECRET', 'your-jwt-secret-key-here');
define('PASSWORD_SALT', 'your-password-salt-here');

// Email configuration
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'WebsiteDeveloper0002.in');

// Payment gateway (Razorpay)
define('RAZORPAY_KEY_ID', 'your-razorpay-key-id');
define('RAZORPAY_KEY_SECRET', 'your-razorpay-key-secret');
define('RAZORPAY_WEBHOOK_SECRET', 'your-razorpay-webhook-secret');

// Google services
define('GOOGLE_ANALYTICS_ID', 'your-google-analytics-id');
define('RECAPTCHA_SITE_KEY', 'your-recaptcha-site-key');
define('RECAPTCHA_SECRET_KEY', 'your-recaptcha-secret-key');

// File upload settings
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']);

// Pagination
define('ITEMS_PER_PAGE', 10);

// Language settings
define('DEFAULT_LANGUAGE', 'en');
define('SUPPORTED_LANGUAGES', ['en', 'hi']);

// Timezone
date_default_timezone_set('Asia/Kolkata');

// Include required files
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/../includes/functions.php';

// Initialize global database instance
global $db;

// Include auth after database is initialized
require_once __DIR__ . '/../includes/auth.php';

// Initialize global auth instance
global $auth;
$auth = new Auth();

// Auto-load classes
spl_autoload_register(function ($class) {
    $file = __DIR__ . '/../classes/' . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// Set default language from session or default
$current_language = $_SESSION['language'] ?? DEFAULT_LANGUAGE;

// Helper function to get current language
function getCurrentLanguage() {
    global $current_language;
    return $current_language;
}

// Helper function to set language
function setLanguage($lang) {
    global $current_language;
    if (in_array($lang, SUPPORTED_LANGUAGES)) {
        $_SESSION['language'] = $lang;
        $current_language = $lang;
        return true;
    }
    return false;
}

// Helper function to get localized text
function __($key, $params = []) {
    global $translations, $current_language;
    
    // Load translations if not loaded
    if (!isset($translations)) {
        $translations = [];
        $lang_file = __DIR__ . '/../languages/' . $current_language . '.php';
        if (file_exists($lang_file)) {
            $translations = require $lang_file;
        }
    }
    
    $text = $translations[$key] ?? $key;
    
    // Replace parameters
    foreach ($params as $param => $value) {
        $text = str_replace('{{' . $param . '}}', $value, $text);
    }
    
    return $text;
}

// CSRF Protection
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Initialize CSRF token
generateCSRFToken();
?>
