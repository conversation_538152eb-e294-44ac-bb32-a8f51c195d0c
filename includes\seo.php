<?php
/**
 * SEO and Analytics Functions
 */

require_once __DIR__ . '/../config/config.php';

/**
 * Generate SEO meta tags
 */
function generateSEOTags($page_data = []) {
    $defaults = [
        'title' => SITE_NAME . ' - Professional Web Development Services in India',
        'description' => 'Professional web development services in India. We create responsive websites, e-commerce solutions, and custom web applications. Get your business online today!',
        'keywords' => 'web development, website design, e-commerce, India, responsive design, custom websites, business websites',
        'image' => SITE_URL . '/assets/images/og-image.jpg',
        'url' => getCurrentURL(),
        'type' => 'website',
        'locale' => getCurrentLanguage() === 'hi' ? 'hi_IN' : 'en_US'
    ];
    
    $seo = array_merge($defaults, $page_data);
    
    $tags = [];
    
    // Basic meta tags
    $tags[] = '<title>' . htmlspecialchars($seo['title']) . '</title>';
    $tags[] = '<meta name="description" content="' . htmlspecialchars($seo['description']) . '">';
    $tags[] = '<meta name="keywords" content="' . htmlspecialchars($seo['keywords']) . '">';
    $tags[] = '<meta name="robots" content="index, follow">';
    $tags[] = '<meta name="author" content="' . SITE_NAME . '">';
    $tags[] = '<link rel="canonical" href="' . htmlspecialchars($seo['url']) . '">';
    
    // Open Graph tags
    $tags[] = '<meta property="og:title" content="' . htmlspecialchars($seo['title']) . '">';
    $tags[] = '<meta property="og:description" content="' . htmlspecialchars($seo['description']) . '">';
    $tags[] = '<meta property="og:image" content="' . htmlspecialchars($seo['image']) . '">';
    $tags[] = '<meta property="og:url" content="' . htmlspecialchars($seo['url']) . '">';
    $tags[] = '<meta property="og:type" content="' . htmlspecialchars($seo['type']) . '">';
    $tags[] = '<meta property="og:locale" content="' . htmlspecialchars($seo['locale']) . '">';
    $tags[] = '<meta property="og:site_name" content="' . SITE_NAME . '">';
    
    // Twitter Card tags
    $tags[] = '<meta name="twitter:card" content="summary_large_image">';
    $tags[] = '<meta name="twitter:title" content="' . htmlspecialchars($seo['title']) . '">';
    $tags[] = '<meta name="twitter:description" content="' . htmlspecialchars($seo['description']) . '">';
    $tags[] = '<meta name="twitter:image" content="' . htmlspecialchars($seo['image']) . '">';
    
    // Additional meta tags
    $tags[] = '<meta name="viewport" content="width=device-width, initial-scale=1.0">';
    $tags[] = '<meta name="theme-color" content="#007bff">';
    $tags[] = '<meta name="msapplication-TileColor" content="#007bff">';
    
    return implode("\n    ", $tags);
}

/**
 * Generate structured data (JSON-LD)
 */
function generateStructuredData($type = 'organization', $data = []) {
    $structured_data = [];
    
    switch ($type) {
        case 'organization':
            $structured_data = [
                '@context' => 'https://schema.org',
                '@type' => 'Organization',
                'name' => SITE_NAME,
                'url' => SITE_URL,
                'logo' => SITE_URL . '/assets/images/logo.png',
                'description' => 'Professional web development services in India',
                'address' => [
                    '@type' => 'PostalAddress',
                    'addressCountry' => 'IN',
                    'addressLocality' => 'India'
                ],
                'contactPoint' => [
                    '@type' => 'ContactPoint',
                    'telephone' => SITE_PHONE,
                    'contactType' => 'customer service',
                    'email' => SITE_EMAIL
                ],
                'sameAs' => [
                    // Add social media URLs here
                ]
            ];
            break;
            
        case 'service':
            $structured_data = [
                '@context' => 'https://schema.org',
                '@type' => 'Service',
                'name' => $data['name'] ?? 'Web Development Services',
                'description' => $data['description'] ?? 'Professional web development services',
                'provider' => [
                    '@type' => 'Organization',
                    'name' => SITE_NAME,
                    'url' => SITE_URL
                ],
                'areaServed' => 'India',
                'serviceType' => 'Web Development'
            ];
            break;
            
        case 'breadcrumb':
            $structured_data = [
                '@context' => 'https://schema.org',
                '@type' => 'BreadcrumbList',
                'itemListElement' => $data
            ];
            break;
            
        case 'faq':
            $structured_data = [
                '@context' => 'https://schema.org',
                '@type' => 'FAQPage',
                'mainEntity' => $data
            ];
            break;
    }
    
    return '<script type="application/ld+json">' . json_encode($structured_data, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) . '</script>';
}

/**
 * Generate breadcrumb navigation
 */
function generateBreadcrumb($items) {
    $breadcrumb_html = '<nav aria-label="breadcrumb"><ol class="breadcrumb">';
    $structured_items = [];
    
    foreach ($items as $index => $item) {
        $position = $index + 1;
        $is_last = $index === count($items) - 1;
        
        if ($is_last) {
            $breadcrumb_html .= '<li class="breadcrumb-item active" aria-current="page">' . htmlspecialchars($item['name']) . '</li>';
        } else {
            $breadcrumb_html .= '<li class="breadcrumb-item"><a href="' . htmlspecialchars($item['url']) . '">' . htmlspecialchars($item['name']) . '</a></li>';
        }
        
        // Add to structured data
        $structured_items[] = [
            '@type' => 'ListItem',
            'position' => $position,
            'name' => $item['name'],
            'item' => $item['url'] ?? null
        ];
    }
    
    $breadcrumb_html .= '</ol></nav>';
    
    // Add structured data
    $breadcrumb_html .= generateStructuredData('breadcrumb', $structured_items);
    
    return $breadcrumb_html;
}

/**
 * Track analytics event
 */
function trackAnalyticsEvent($event_name, $event_data = [], $user_id = null) {
    global $db;
    
    try {
        $data = [
            'event_type' => $event_name,
            'event_data' => json_encode($event_data),
            'user_id' => $user_id,
            'session_id' => session_id(),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        return $db->insert('analytics_events', $data);
    } catch (Exception $e) {
        logActivity("Analytics tracking error: " . $e->getMessage(), 'error');
        return false;
    }
}

/**
 * Get current URL
 */
function getCurrentURL() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $uri = $_SERVER['REQUEST_URI'] ?? '';
    
    return $protocol . '://' . $host . $uri;
}

/**
 * Generate sitemap XML
 */
function generateSitemap() {
    global $db;
    
    $urls = [
        [
            'loc' => SITE_URL . '/',
            'changefreq' => 'weekly',
            'priority' => '1.0',
            'lastmod' => date('Y-m-d')
        ],
        [
            'loc' => SITE_URL . '/services.php',
            'changefreq' => 'monthly',
            'priority' => '0.9',
            'lastmod' => date('Y-m-d')
        ],
        [
            'loc' => SITE_URL . '/contact.php',
            'changefreq' => 'monthly',
            'priority' => '0.8',
            'lastmod' => date('Y-m-d')
        ],
        [
            'loc' => SITE_URL . '/about.php',
            'changefreq' => 'monthly',
            'priority' => '0.7',
            'lastmod' => date('Y-m-d')
        ]
    ];
    
    // Add dynamic pages (like service packages)
    $packages = $db->fetchAll('SELECT id, updated_at FROM service_packages WHERE is_active = 1');
    foreach ($packages as $package) {
        $urls[] = [
            'loc' => SITE_URL . '/service-details.php?id=' . $package['id'],
            'changefreq' => 'monthly',
            'priority' => '0.6',
            'lastmod' => date('Y-m-d', strtotime($package['updated_at']))
        ];
    }
    
    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    
    foreach ($urls as $url) {
        $xml .= '  <url>' . "\n";
        $xml .= '    <loc>' . htmlspecialchars($url['loc']) . '</loc>' . "\n";
        $xml .= '    <lastmod>' . $url['lastmod'] . '</lastmod>' . "\n";
        $xml .= '    <changefreq>' . $url['changefreq'] . '</changefreq>' . "\n";
        $xml .= '    <priority>' . $url['priority'] . '</priority>' . "\n";
        $xml .= '  </url>' . "\n";
    }
    
    $xml .= '</urlset>';
    
    return $xml;
}

/**
 * Generate robots.txt
 */
function generateRobotsTxt() {
    $robots = "User-agent: *\n";
    $robots .= "Allow: /\n";
    $robots .= "Disallow: /admin/\n";
    $robots .= "Disallow: /config/\n";
    $robots .= "Disallow: /includes/\n";
    $robots .= "Disallow: /database/\n";
    $robots .= "\n";
    $robots .= "Sitemap: " . SITE_URL . "/sitemap.xml\n";
    
    return $robots;
}

/**
 * Get page-specific SEO data
 */
function getPageSEO($page) {
    $seo_data = [
        'home' => [
            'title' => SITE_NAME . ' - Professional Web Development Services in India',
            'description' => 'Get professional web development services in India. We create responsive websites, e-commerce solutions, and custom web applications for businesses.',
            'keywords' => 'web development India, website design, e-commerce development, responsive websites, custom web applications'
        ],
        'services' => [
            'title' => 'Web Development Services - ' . SITE_NAME,
            'description' => 'Explore our comprehensive web development services including business websites, e-commerce solutions, and custom development packages.',
            'keywords' => 'web development services, business websites, e-commerce development, custom web applications, website packages'
        ],
        'contact' => [
            'title' => 'Contact Us - ' . SITE_NAME,
            'description' => 'Get in touch with our web development team. Contact us for quotes, consultations, and support for your web development needs.',
            'keywords' => 'contact web developer, web development consultation, website quote, technical support'
        ],
        'about' => [
            'title' => 'About Us - ' . SITE_NAME,
            'description' => 'Learn about our web development company, our team, and our commitment to delivering high-quality websites and web applications.',
            'keywords' => 'about web development company, web development team, company profile, web development expertise'
        ]
    ];
    
    return $seo_data[$page] ?? [];
}

/**
 * Generate Google Analytics code
 */
function generateGoogleAnalytics() {
    $ga_id = GOOGLE_ANALYTICS_ID;
    
    if (empty($ga_id) || $ga_id === 'your-google-analytics-id') {
        return '';
    }
    
    return "
    <!-- Google Analytics -->
    <script async src=\"https://www.googletagmanager.com/gtag/js?id={$ga_id}\"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '{$ga_id}');
        
        // Custom event tracking
        function trackEvent(action, category, label, value) {
            gtag('event', action, {
                'event_category': category,
                'event_label': label,
                'value': value
            });
        }
        
        // Track form submissions
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            forms.forEach(function(form) {
                form.addEventListener('submit', function() {
                    const formId = this.id || 'unknown';
                    trackEvent('form_submit', 'engagement', formId);
                });
            });
            
            // Track button clicks
            const buttons = document.querySelectorAll('.btn-primary, .btn-success');
            buttons.forEach(function(button) {
                button.addEventListener('click', function() {
                    const buttonText = this.textContent.trim();
                    trackEvent('button_click', 'engagement', buttonText);
                });
            });
        });
    </script>
    ";
}

/**
 * Optimize images for SEO
 */
function optimizeImageSEO($src, $alt, $title = '', $lazy = true) {
    $attributes = [
        'src' => $src,
        'alt' => htmlspecialchars($alt),
        'loading' => $lazy ? 'lazy' : 'eager'
    ];
    
    if ($title) {
        $attributes['title'] = htmlspecialchars($title);
    }
    
    $attr_string = '';
    foreach ($attributes as $key => $value) {
        $attr_string .= ' ' . $key . '="' . $value . '"';
    }
    
    return '<img' . $attr_string . '>';
}

/**
 * Generate meta tags for social sharing
 */
function generateSocialTags($data) {
    $tags = [];
    
    // Facebook Open Graph
    if (isset($data['title'])) {
        $tags[] = '<meta property="og:title" content="' . htmlspecialchars($data['title']) . '">';
    }
    if (isset($data['description'])) {
        $tags[] = '<meta property="og:description" content="' . htmlspecialchars($data['description']) . '">';
    }
    if (isset($data['image'])) {
        $tags[] = '<meta property="og:image" content="' . htmlspecialchars($data['image']) . '">';
    }
    
    // Twitter Cards
    $tags[] = '<meta name="twitter:card" content="summary_large_image">';
    if (isset($data['title'])) {
        $tags[] = '<meta name="twitter:title" content="' . htmlspecialchars($data['title']) . '">';
    }
    if (isset($data['description'])) {
        $tags[] = '<meta name="twitter:description" content="' . htmlspecialchars($data['description']) . '">';
    }
    if (isset($data['image'])) {
        $tags[] = '<meta name="twitter:image" content="' . htmlspecialchars($data['image']) . '">';
    }
    
    return implode("\n    ", $tags);
}

/**
 * Track page view
 */
function trackPageView($page_name = null) {
    $page_name = $page_name ?? basename($_SERVER['PHP_SELF'], '.php');
    
    trackAnalyticsEvent('page_view', [
        'page' => $page_name,
        'url' => getCurrentURL(),
        'referrer' => $_SERVER['HTTP_REFERER'] ?? '',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ]);
}

// Auto-track page views
if (!defined('DISABLE_AUTO_TRACKING')) {
    trackPageView();
}
?>
