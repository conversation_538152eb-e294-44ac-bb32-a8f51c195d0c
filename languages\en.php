<?php
/**
 * English Language File
 * websitedeveloper0002.in
 */

return [
    // Common
    'site_name' => 'WebsiteDeveloper0002.in',
    'welcome' => 'Welcome',
    'home' => 'Home',
    'about' => 'About',
    'services' => 'Services',
    'contact' => 'Contact',
    'login' => 'Login',
    'register' => 'Register',
    'logout' => 'Logout',
    'dashboard' => 'Dashboard',
    'profile' => 'Profile',
    'settings' => 'Settings',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'delete' => 'Delete',
    'edit' => 'Edit',
    'view' => 'View',
    'back' => 'Back',
    'next' => 'Next',
    'previous' => 'Previous',
    'submit' => 'Submit',
    'loading' => 'Loading...',
    'search' => 'Search',
    'filter' => 'Filter',
    'sort' => 'Sort',
    'date' => 'Date',
    'time' => 'Time',
    'status' => 'Status',
    'action' => 'Action',
    'actions' => 'Actions',
    'yes' => 'Yes',
    'no' => 'No',
    'ok' => 'OK',
    'error' => 'Error',
    'success' => 'Success',
    'warning' => 'Warning',
    'info' => 'Information',
    
    // Navigation
    'nav_home' => 'Home',
    'nav_services' => 'Our Services',
    'nav_about' => 'About Us',
    'nav_contact' => 'Contact',
    'nav_login' => 'Login',
    'nav_register' => 'Register',
    'nav_dashboard' => 'Dashboard',
    'nav_profile' => 'Profile',
    'nav_logout' => 'Logout',
    
    // Homepage
    'hero_title' => 'Professional Web Development Services',
    'hero_subtitle' => 'Transform your business with our premium web solutions',
    'hero_cta' => 'Get Started Today',
    'why_choose_us' => 'Why Choose Us?',
    'our_services' => 'Our Services',
    'testimonials' => 'What Our Clients Say',
    'get_quote' => 'Get Free Quote',
    
    // Service Packages
    'business_package' => 'BUSINESS PREMIUM PACKAGE',
    'business_price' => '₹15,000',
    'business_description' => 'Complete business website solution with authentication and email automation',
    'ecommerce_package' => 'E-COMMERCE PACKAGE',
    'ecommerce_price' => '₹75,000',
    'ecommerce_description' => 'Complete e-commerce solution with multi-vendor support and payment gateway',
    'custom_package' => 'CUSTOM DEVELOPMENT',
    'custom_price' => 'Price on Request',
    'custom_description' => 'Custom solutions: CRM, booking systems, LMS, portals, automations',
    'enquire_now' => 'Enquire Now',
    'view_details' => 'View Details',
    'start_my_store' => 'Start My Store',
    'one_time_payment' => 'One-time Payment',
    
    // Features
    'features' => 'Features',
    'feature_domain_hosting' => 'Free Domain & Hosting Setup',
    'feature_multipage' => 'Multi-page website with secure authentication',
    'feature_dashboard' => 'Query Dashboard for inquiries',
    'feature_email_automation' => 'Automatic Email Sender (500 emails/month)',
    'feature_recaptcha' => 'reCAPTCHA integration',
    'feature_seo' => 'On-page SEO optimization',
    'feature_analytics' => 'Google Analytics integration',
    'feature_support_links' => 'WhatsApp/Phone/Email support links',
    'feature_ecommerce_storefront' => 'Multi-page storefront with product catalog',
    'feature_three_dashboards' => 'Three separate dashboards (Admin/Sellers/Customers)',
    'feature_role_access' => 'Role-based access control system',
    'feature_payment_gateway' => 'Payment Gateway Integration (Razorpay)',
    'feature_order_emails' => 'Order confirmations & abandoned cart recovery',
    'feature_custom_solutions' => 'Custom solutions: CRM, booking systems, LMS',
    'feature_modular_pricing' => 'Modular pricing - pay only for required features',
    
    // Authentication
    'username' => 'Username',
    'email' => 'Email',
    'password' => 'Password',
    'confirm_password' => 'Confirm Password',
    'full_name' => 'Full Name',
    'phone' => 'Phone Number',
    'remember_me' => 'Remember Me',
    'forgot_password' => 'Forgot Password?',
    'create_account' => 'Create Account',
    'already_have_account' => 'Already have an account?',
    'dont_have_account' => "Don't have an account?",
    'sign_in_here' => 'Sign in here',
    'sign_up_here' => 'Sign up here',
    
    // Messages
    'user_already_exists' => 'User already exists with this username or email',
    'invalid_registration_data' => 'Please provide valid registration information',
    'registration_successful' => 'Registration successful! Please check your email for verification',
    'registration_failed' => 'Registration failed. Please try again',
    'registration_error' => 'An error occurred during registration',
    'invalid_credentials' => 'Invalid username/email or password',
    'account_suspended' => 'Your account has been suspended',
    'login_successful' => 'Login successful!',
    'login_error' => 'An error occurred during login',
    'logout_successful' => 'You have been logged out successfully',
    'logout_error' => 'An error occurred during logout',
    'current_password_incorrect' => 'Current password is incorrect',
    'password_changed_successfully' => 'Password changed successfully',
    'password_change_error' => 'An error occurred while changing password',
    'email_not_found' => 'No account found with this email address',
    'password_reset_email_sent' => 'Password reset instructions sent to your email',
    'password_reset_error' => 'An error occurred while processing password reset',
    
    // Contact Form
    'contact_us' => 'Contact Us',
    'contact_form' => 'Contact Form',
    'your_name' => 'Your Name',
    'your_email' => 'Your Email',
    'your_phone' => 'Your Phone',
    'subject' => 'Subject',
    'message' => 'Message',
    'send_message' => 'Send Message',
    'contact_info' => 'Contact Information',
    'our_address' => 'Our Address',
    'call_us' => 'Call Us',
    'email_us' => 'Email Us',
    'whatsapp_us' => 'WhatsApp Us',
    'message_sent_successfully' => 'Your message has been sent successfully!',
    'message_send_error' => 'An error occurred while sending your message',
    
    // Dashboard
    'welcome_back' => 'Welcome back',
    'my_orders' => 'My Orders',
    'my_profile' => 'My Profile',
    'order_history' => 'Order History',
    'recent_orders' => 'Recent Orders',
    'total_orders' => 'Total Orders',
    'pending_orders' => 'Pending Orders',
    'completed_orders' => 'Completed Orders',
    'order_number' => 'Order Number',
    'order_date' => 'Order Date',
    'package_name' => 'Package',
    'amount' => 'Amount',
    'order_status' => 'Status',
    'payment_status' => 'Payment Status',
    'view_order' => 'View Order',
    
    // Admin
    'admin_dashboard' => 'Admin Dashboard',
    'manage_users' => 'Manage Users',
    'manage_orders' => 'Manage Orders',
    'manage_inquiries' => 'Manage Inquiries',
    'manage_packages' => 'Manage Packages',
    'site_settings' => 'Site Settings',
    'analytics' => 'Analytics',
    'reports' => 'Reports',
    'total_users' => 'Total Users',
    'total_revenue' => 'Total Revenue',
    'new_inquiries' => 'New Inquiries',
    'active_orders' => 'Active Orders',
    
    // Footer
    'footer_about' => 'About WebsiteDeveloper0002.in',
    'footer_about_text' => 'We are a professional web development company specializing in creating high-quality websites and web applications for businesses across India.',
    'footer_services' => 'Our Services',
    'footer_contact' => 'Contact Info',
    'footer_follow' => 'Follow Us',
    'footer_copyright' => '© 2025 WebsiteDeveloper0002.in. All rights reserved.',
    'footer_privacy' => 'Privacy Policy',
    'footer_terms' => 'Terms of Service',
    
    // Language
    'language' => 'Language',
    'english' => 'English',
    'hindi' => 'हिंदी',
    'change_language' => 'Change Language',
    
    // Validation
    'field_required' => 'This field is required',
    'invalid_email' => 'Please enter a valid email address',
    'invalid_phone' => 'Please enter a valid phone number',
    'password_min_length' => 'Password must be at least 6 characters long',
    'passwords_not_match' => 'Passwords do not match',
    'invalid_file_type' => 'Invalid file type',
    'file_too_large' => 'File size is too large',
    
    // Payment
    'payment' => 'Payment',
    'pay_now' => 'Pay Now',
    'payment_successful' => 'Payment successful!',
    'payment_failed' => 'Payment failed. Please try again.',
    'payment_pending' => 'Payment is pending',
    'order_total' => 'Order Total',
    'payment_method' => 'Payment Method',
    'card_payment' => 'Card Payment',
    'net_banking' => 'Net Banking',
    'upi_payment' => 'UPI Payment',
    'wallet_payment' => 'Wallet Payment',
];
?>
