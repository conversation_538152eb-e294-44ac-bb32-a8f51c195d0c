    </main>
    
    <!-- Footer -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-code me-2 text-primary"></i><?php echo __('site_name'); ?>
                    </h5>
                    <p class="text-muted"><?php echo __('footer_about_text'); ?></p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3"><?php echo __('footer_services'); ?></h6>
                    <ul class="list-unstyled">
                        <li><a href="<?php echo SITE_URL; ?>/services.php#business" class="text-muted text-decoration-none"><?php echo __('business_package'); ?></a></li>
                        <li><a href="<?php echo SITE_URL; ?>/services.php#ecommerce" class="text-muted text-decoration-none"><?php echo __('ecommerce_package'); ?></a></li>
                        <li><a href="<?php echo SITE_URL; ?>/services.php#custom" class="text-muted text-decoration-none"><?php echo __('custom_package'); ?></a></li>
                    </ul>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3"><?php echo __('footer_contact'); ?></h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-envelope me-2 text-primary"></i>
                            <a href="mailto:<?php echo SITE_EMAIL; ?>" class="text-muted text-decoration-none"><?php echo SITE_EMAIL; ?></a>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-phone me-2 text-primary"></i>
                            <a href="tel:<?php echo SITE_PHONE; ?>" class="text-muted text-decoration-none"><?php echo SITE_PHONE; ?></a>
                        </li>
                        <li class="mb-2">
                            <i class="fab fa-whatsapp me-2 text-primary"></i>
                            <a href="https://wa.me/<?php echo str_replace(['+', '-', ' '], '', WHATSAPP_NUMBER); ?>" class="text-muted text-decoration-none" target="_blank">WhatsApp</a>
                        </li>
                    </ul>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3"><?php echo __('get_quote'); ?></h6>
                    <p class="text-muted small"><?php echo __('contact_us'); ?></p>
                    <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-paper-plane me-2"></i><?php echo __('contact_us'); ?>
                    </a>
                </div>
            </div>
            
            <hr class="my-4 border-secondary">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted small"><?php echo __('footer_copyright'); ?></p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="<?php echo SITE_URL; ?>/privacy.php" class="text-muted text-decoration-none small me-3"><?php echo __('footer_privacy'); ?></a>
                    <a href="<?php echo SITE_URL; ?>/terms.php" class="text-muted text-decoration-none small"><?php echo __('footer_terms'); ?></a>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- WhatsApp Float Button -->
    <div class="whatsapp-float">
        <a href="https://wa.me/<?php echo str_replace(['+', '-', ' '], '', WHATSAPP_NUMBER); ?>?text=Hi%2C%20I%20am%20interested%20in%20your%20web%20development%20services" 
           target="_blank" class="btn btn-success rounded-circle p-3 shadow">
            <i class="fab fa-whatsapp fa-lg"></i>
        </a>
    </div>
    
    <!-- Back to Top Button -->
    <div class="back-to-top">
        <button class="btn btn-primary rounded-circle p-3 shadow" onclick="scrollToTop()">
            <i class="fas fa-arrow-up"></i>
        </button>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo SITE_URL; ?>/assets/js/main.js"></script>
    
    <script>
        // Back to top functionality
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
        
        // Show/hide back to top button
        window.addEventListener('scroll', function() {
            const backToTop = document.querySelector('.back-to-top');
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'block';
            } else {
                backToTop.style.display = 'none';
            }
        });
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Language switcher
        document.addEventListener('DOMContentLoaded', function() {
            const languageLinks = document.querySelectorAll('a[href*="lang="]');
            languageLinks.forEach(function(link) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const url = new URL(window.location);
                    const lang = this.href.split('lang=')[1];
                    url.searchParams.set('lang', lang);
                    window.location.href = url.toString();
                });
            });
        });
        
        // Google Analytics events
        function trackEvent(action, category, label) {
            if (typeof gtag !== 'undefined') {
                gtag('event', action, {
                    'event_category': category,
                    'event_label': label
                });
            }
        }
        
        // Track button clicks
        document.addEventListener('click', function(e) {
            if (e.target.matches('.btn-primary, .btn-success')) {
                const buttonText = e.target.textContent.trim();
                trackEvent('click', 'button', buttonText);
            }
        });
    </script>
    
    <?php if (isset($additional_js)): ?>
        <?php echo $additional_js; ?>
    <?php endif; ?>
</body>
</html>
