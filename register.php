<?php
$page_title = __('register');
require_once 'includes/header.php';

// Redirect if already logged in
if ($auth->isLoggedIn()) {
    redirect('/dashboard.php');
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize($_POST['username'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $full_name = sanitize($_POST['full_name'] ?? '');
    $phone = sanitize($_POST['phone'] ?? '');
    
    // Validation
    if (empty($username) || empty($email) || empty($password) || empty($full_name)) {
        $error = __('field_required');
    } elseif (!isValidEmail($email)) {
        $error = __('invalid_email');
    } elseif (strlen($password) < 6) {
        $error = __('password_min_length');
    } elseif ($password !== $confirm_password) {
        $error = __('passwords_not_match');
    } elseif ($phone && !isValidPhone($phone)) {
        $error = __('invalid_phone');
    } else {
        // reCAPTCHA validation
        if (defined('RECAPTCHA_SECRET_KEY') && RECAPTCHA_SECRET_KEY) {
            $recaptcha_response = $_POST['g-recaptcha-response'] ?? '';
            if (empty($recaptcha_response)) {
                $error = 'Please complete the reCAPTCHA verification';
            } else {
                $verify_url = 'https://www.google.com/recaptcha/api/siteverify';
                $verify_data = [
                    'secret' => RECAPTCHA_SECRET_KEY,
                    'response' => $recaptcha_response,
                    'remoteip' => getClientIP()
                ];
                
                $verify_response = file_get_contents($verify_url . '?' . http_build_query($verify_data));
                $verify_result = json_decode($verify_response, true);
                
                if (!$verify_result['success']) {
                    $error = 'reCAPTCHA verification failed';
                }
            }
        }
        
        if (!$error) {
            $result = $auth->register($username, $email, $password, $full_name, $phone);
            
            if ($result['success']) {
                $success = $result['message'];
            } else {
                $error = $result['message'];
            }
        }
    }
}
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-header text-center">
                    <h3 class="mb-0">
                        <i class="fas fa-user-plus me-2 text-primary"></i>
                        <?php echo __('create_account'); ?>
                    </h3>
                </div>
                <div class="card-body p-4">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        </div>
                        <div class="text-center">
                            <a href="<?php echo SITE_URL; ?>/login.php" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt me-2"></i><?php echo __('login'); ?>
                            </a>
                        </div>
                    <?php else: ?>
                    
                    <form method="POST" action="" id="registerForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label"><?php echo __('full_name'); ?> *</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="<?php echo htmlspecialchars($full_name ?? ''); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label"><?php echo __('username'); ?> *</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($username ?? ''); ?>" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label"><?php echo __('email'); ?> *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label"><?php echo __('phone'); ?></label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($phone ?? ''); ?>" 
                                       placeholder="+91-9999999999">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label"><?php echo __('password'); ?> *</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                        <i class="fas fa-eye" id="toggleIcon1"></i>
                                    </button>
                                </div>
                                <small class="text-muted"><?php echo __('password_min_length'); ?></small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label"><?php echo __('confirm_password'); ?> *</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                        <i class="fas fa-eye" id="toggleIcon2"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <?php if (defined('RECAPTCHA_SITE_KEY') && RECAPTCHA_SITE_KEY): ?>
                        <div class="mb-3">
                            <div class="g-recaptcha" data-sitekey="<?php echo RECAPTCHA_SITE_KEY; ?>"></div>
                        </div>
                        <?php endif; ?>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="<?php echo SITE_URL; ?>/terms.php" target="_blank">Terms of Service</a> 
                                and <a href="<?php echo SITE_URL; ?>/privacy.php" target="_blank">Privacy Policy</a>
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-user-plus me-2"></i><?php echo __('create_account'); ?>
                        </button>
                    </form>
                    
                    <?php endif; ?>
                </div>
                <div class="card-footer text-center">
                    <p class="mb-0">
                        <?php echo __('already_have_account'); ?>
                        <a href="<?php echo SITE_URL; ?>/login.php" class="text-decoration-none fw-bold">
                            <?php echo __('sign_in_here'); ?>
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const toggleIcon = document.getElementById(fieldId === 'password' ? 'toggleIcon1' : 'toggleIcon2');
    
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('registerForm');
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePasswords() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('<?php echo __('passwords_not_match'); ?>');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    password.addEventListener('input', validatePasswords);
    confirmPassword.addEventListener('input', validatePasswords);
    
    // Auto-focus on full name field
    document.getElementById('full_name').focus();
});
</script>

<?php require_once 'includes/footer.php'; ?>
