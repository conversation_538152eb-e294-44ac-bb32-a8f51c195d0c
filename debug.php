<?php
// Simple debug file to check what's causing the error
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "PHP Version: " . PHP_VERSION . "<br>";
echo "Current directory: " . __DIR__ . "<br>";

// Test if config file exists
if (file_exists(__DIR__ . '/config/config.php')) {
    echo "Config file exists<br>";
    
    try {
        require_once __DIR__ . '/config/config.php';
        echo "Config file loaded successfully<br>";
        echo "Site Name: " . SITE_NAME . "<br>";
        echo "Database Name: " . DB_NAME . "<br>";
    } catch (Exception $e) {
        echo "Error loading config: " . $e->getMessage() . "<br>";
    }
} else {
    echo "Config file does not exist<br>";
}

// Test database connection
try {
    $pdo = new PDO("mysql:host=localhost;dbname=websitedeveloper0002", "root", "");
    echo "Database connection successful<br>";
} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "<br>";
}

echo "Debug complete.";
?>
