<?php
$page_title = __('dashboard');
require_once 'includes/header.php';

// Require login
$auth->requireLogin();

$user = $auth->getCurrentUser();

// Get user statistics
$stats = [
    'total_orders' => $db->fetch('SELECT COUNT(*) as count FROM orders WHERE user_id = ?', [$user['id']])['count'],
    'pending_orders' => $db->fetch('SELECT COUNT(*) as count FROM orders WHERE user_id = ? AND status = "pending"', [$user['id']])['count'],
    'completed_orders' => $db->fetch('SELECT COUNT(*) as count FROM orders WHERE user_id = ? AND status = "completed"', [$user['id']])['count'],
    'total_spent' => $db->fetch('SELECT COALESCE(SUM(total_amount), 0) as total FROM orders WHERE user_id = ? AND payment_status = "paid"', [$user['id']])['total']
];

// Get recent orders
$recent_orders = $db->fetchAll(
    'SELECT o.*, sp.name_en, sp.name_hi, sp.package_type 
     FROM orders o 
     JOIN service_packages sp ON o.package_id = sp.id 
     WHERE o.user_id = ? 
     ORDER BY o.created_at DESC 
     LIMIT 5',
    [$user['id']]
);

// Get recent inquiries if user has made any
$recent_inquiries = $db->fetchAll(
    'SELECT * FROM inquiries WHERE email = ? ORDER BY created_at DESC LIMIT 3',
    [$user['email']]
);
?>

<div class="container py-5">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white shadow-custom border-radius-custom">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2"><?php echo __('welcome_back'); ?>, <?php echo htmlspecialchars($user['full_name']); ?>!</h2>
                            <p class="mb-0 opacity-75">
                                Manage your orders, track progress, and explore our services from your dashboard.
                            </p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <i class="fas fa-user-circle" style="font-size: 4rem; opacity: 0.3;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card shadow-custom border-radius-custom h-100">
                <div class="card-body text-center">
                    <div class="service-icon mx-auto mb-3" style="width: 60px; height: 60px; font-size: 1.5rem;">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h3 class="text-primary mb-1"><?php echo $stats['total_orders']; ?></h3>
                    <p class="text-muted mb-0"><?php echo __('total_orders'); ?></p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card shadow-custom border-radius-custom h-100">
                <div class="card-body text-center">
                    <div class="service-icon mx-auto mb-3" style="width: 60px; height: 60px; font-size: 1.5rem; background: linear-gradient(135deg, #ffc107, #ff8c00);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="text-warning mb-1"><?php echo $stats['pending_orders']; ?></h3>
                    <p class="text-muted mb-0"><?php echo __('pending_orders'); ?></p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card shadow-custom border-radius-custom h-100">
                <div class="card-body text-center">
                    <div class="service-icon mx-auto mb-3" style="width: 60px; height: 60px; font-size: 1.5rem; background: linear-gradient(135deg, #28a745, #20c997);">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="text-success mb-1"><?php echo $stats['completed_orders']; ?></h3>
                    <p class="text-muted mb-0"><?php echo __('completed_orders'); ?></p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card shadow-custom border-radius-custom h-100">
                <div class="card-body text-center">
                    <div class="service-icon mx-auto mb-3" style="width: 60px; height: 60px; font-size: 1.5rem; background: linear-gradient(135deg, #6f42c1, #e83e8c);">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <h3 class="text-info mb-1"><?php echo formatCurrency($stats['total_spent']); ?></h3>
                    <p class="text-muted mb-0">Total Spent</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Recent Orders -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2 text-primary"></i>
                        <?php echo __('recent_orders'); ?>
                    </h5>
                    <a href="<?php echo SITE_URL; ?>/orders.php" class="btn btn-outline-primary btn-sm">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_orders)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-shopping-cart text-muted" style="font-size: 3rem; opacity: 0.3;"></i>
                            <h6 class="text-muted mt-3">No orders yet</h6>
                            <p class="text-muted">Start by exploring our services and placing your first order.</p>
                            <a href="<?php echo SITE_URL; ?>/services.php" class="btn btn-primary">
                                <i class="fas fa-eye me-2"></i>Browse Services
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th><?php echo __('order_number'); ?></th>
                                        <th><?php echo __('package_name'); ?></th>
                                        <th><?php echo __('amount'); ?></th>
                                        <th><?php echo __('status'); ?></th>
                                        <th><?php echo __('date'); ?></th>
                                        <th><?php echo __('action'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_orders as $order): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo $order['order_number']; ?></strong>
                                            </td>
                                            <td>
                                                <?php echo getCurrentLanguage() === 'hi' ? $order['name_hi'] : $order['name_en']; ?>
                                            </td>
                                            <td>
                                                <?php if ($order['total_amount'] > 0): ?>
                                                    <?php echo formatCurrency($order['total_amount']); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Quote</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = [
                                                    'pending' => 'warning',
                                                    'paid' => 'info',
                                                    'processing' => 'primary',
                                                    'completed' => 'success',
                                                    'cancelled' => 'danger'
                                                ];
                                                $class = $statusClass[$order['status']] ?? 'secondary';
                                                ?>
                                                <span class="badge bg-<?php echo $class; ?>">
                                                    <?php echo ucfirst($order['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php echo formatDate($order['created_at']); ?>
                                            </td>
                                            <td>
                                                <a href="<?php echo SITE_URL; ?>/order-details.php?id=<?php echo $order['id']; ?>" 
                                                   class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions & Info -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card shadow-custom border-radius-custom mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2 text-primary"></i>
                        Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo SITE_URL; ?>/services.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>New Order
                        </a>
                        <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-2"></i>Contact Support
                        </a>
                        <a href="<?php echo SITE_URL; ?>/profile.php" class="btn btn-outline-secondary">
                            <i class="fas fa-user me-2"></i>Edit Profile
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Account Info -->
            <div class="card shadow-custom border-radius-custom mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-user me-2 text-primary"></i>
                        Account Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">Username:</small><br>
                        <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">Email:</small><br>
                        <strong><?php echo htmlspecialchars($user['email']); ?></strong>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">Member Since:</small><br>
                        <strong><?php echo formatDate($user['created_at']); ?></strong>
                    </div>
                    <div>
                        <small class="text-muted">Account Status:</small><br>
                        <span class="badge bg-success">Active</span>
                    </div>
                </div>
            </div>
            
            <!-- Recent Inquiries -->
            <?php if (!empty($recent_inquiries)): ?>
            <div class="card shadow-custom border-radius-custom">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-comments me-2 text-primary"></i>
                        Recent Inquiries
                    </h6>
                </div>
                <div class="card-body">
                    <?php foreach ($recent_inquiries as $inquiry): ?>
                        <div class="border-bottom pb-2 mb-2">
                            <div class="d-flex justify-content-between">
                                <strong class="small"><?php echo htmlspecialchars($inquiry['subject']); ?></strong>
                                <span class="badge bg-<?php echo $inquiry['status'] === 'new' ? 'warning' : 'success'; ?> small">
                                    <?php echo ucfirst($inquiry['status']); ?>
                                </span>
                            </div>
                            <small class="text-muted"><?php echo formatDate($inquiry['created_at']); ?></small>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
