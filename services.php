<?php
$page_title = __('nav_services');
$page_description = 'Explore our comprehensive web development services: Business Premium Package, E-commerce Package, and Custom Development solutions for Indian businesses.';
require_once 'includes/header.php';

// Get service packages from database
$packages = $db->fetchAll('SELECT * FROM service_packages WHERE is_active = 1 ORDER BY id');
?>

<div class="container py-5">
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-gradient"><?php echo __('our_services'); ?></h1>
        <p class="lead text-muted">Choose the perfect package for your business needs</p>
    </div>
    
    <?php foreach ($packages as $index => $package): ?>
        <section id="<?php echo $package['package_type']; ?>" class="mb-5">
            <div class="card shadow-custom border-radius-custom overflow-hidden">
                <div class="card-header bg-primary text-white">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <?php if ($package['package_type'] === 'business'): ?>
                                    <i class="fas fa-briefcase me-3"></i>
                                <?php elseif ($package['package_type'] === 'ecommerce'): ?>
                                    <i class="fas fa-shopping-cart me-3"></i>
                                <?php else: ?>
                                    <i class="fas fa-cogs me-3"></i>
                                <?php endif; ?>
                                <?php echo getCurrentLanguage() === 'hi' ? $package['name_hi'] : $package['name_en']; ?>
                            </h2>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="h2 mb-0">
                                <?php if ($package['price'] > 0): ?>
                                    <?php echo formatCurrency($package['price']); ?>
                                    <small class="d-block"><?php echo __('one_time_payment'); ?></small>
                                <?php else: ?>
                                    <?php echo __('custom_price'); ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-lg-8">
                            <p class="lead mb-4">
                                <?php echo getCurrentLanguage() === 'hi' ? $package['description_hi'] : $package['description_en']; ?>
                            </p>
                            
                            <h4 class="mb-3"><?php echo __('features'); ?></h4>
                            <div class="row">
                                <?php 
                                $features = json_decode(getCurrentLanguage() === 'hi' ? $package['features_hi'] : $package['features_en'], true);
                                $half = ceil(count($features) / 2);
                                ?>
                                <div class="col-md-6">
                                    <ul class="feature-list">
                                        <?php for ($i = 0; $i < $half; $i++): ?>
                                            <?php if (isset($features[$i])): ?>
                                                <li><?php echo $features[$i]; ?></li>
                                            <?php endif; ?>
                                        <?php endfor; ?>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="feature-list">
                                        <?php for ($i = $half; $i < count($features); $i++): ?>
                                            <li><?php echo $features[$i]; ?></li>
                                        <?php endfor; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4">
                            <div class="text-center">
                                <?php if ($package['package_type'] === 'business'): ?>
                                    <div class="service-icon mx-auto mb-3" style="width: 120px; height: 120px; font-size: 3rem;">
                                        <i class="fas fa-briefcase"></i>
                                    </div>
                                    <h5>Perfect for Small Businesses</h5>
                                    <p class="text-muted mb-4">Get your business online with a professional website that converts visitors into customers.</p>
                                    
                                    <?php if ($auth->isLoggedIn()): ?>
                                        <a href="<?php echo SITE_URL; ?>/order.php?package=<?php echo $package['id']; ?>" class="btn btn-primary btn-lg w-100 mb-3">
                                            <i class="fas fa-shopping-cart me-2"></i>Order Now
                                        </a>
                                    <?php else: ?>
                                        <a href="<?php echo SITE_URL; ?>/register.php" class="btn btn-primary btn-lg w-100 mb-3">
                                            <i class="fas fa-user-plus me-2"></i>Register to Order
                                        </a>
                                    <?php endif; ?>
                                    
                                    <a href="<?php echo SITE_URL; ?>/contact.php?package=business" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-paper-plane me-2"></i><?php echo __('enquire_now'); ?>
                                    </a>
                                    
                                <?php elseif ($package['package_type'] === 'ecommerce'): ?>
                                    <div class="service-icon mx-auto mb-3" style="width: 120px; height: 120px; font-size: 3rem;">
                                        <i class="fas fa-shopping-cart"></i>
                                    </div>
                                    <h5>Complete E-commerce Solution</h5>
                                    <p class="text-muted mb-4">Launch your online store with advanced features and multi-vendor capabilities.</p>
                                    
                                    <?php if ($auth->isLoggedIn()): ?>
                                        <a href="<?php echo SITE_URL; ?>/order.php?package=<?php echo $package['id']; ?>" class="btn btn-success btn-lg w-100 mb-3">
                                            <i class="fas fa-store me-2"></i><?php echo __('start_my_store'); ?>
                                        </a>
                                    <?php else: ?>
                                        <a href="<?php echo SITE_URL; ?>/register.php" class="btn btn-success btn-lg w-100 mb-3">
                                            <i class="fas fa-user-plus me-2"></i>Register to Start
                                        </a>
                                    <?php endif; ?>
                                    
                                    <a href="<?php echo SITE_URL; ?>/contact.php?package=ecommerce" class="btn btn-outline-success w-100">
                                        <i class="fas fa-paper-plane me-2"></i><?php echo __('enquire_now'); ?>
                                    </a>
                                    
                                <?php else: ?>
                                    <div class="service-icon mx-auto mb-3" style="width: 120px; height: 120px; font-size: 3rem;">
                                        <i class="fas fa-cogs"></i>
                                    </div>
                                    <h5>Tailored Solutions</h5>
                                    <p class="text-muted mb-4">Get a custom solution built specifically for your unique business requirements.</p>
                                    
                                    <a href="<?php echo SITE_URL; ?>/contact.php?package=custom" class="btn btn-outline-primary btn-lg w-100">
                                        <i class="fas fa-comments me-2"></i>Discuss Requirements
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php endforeach; ?>
    
    <!-- Comparison Table -->
    <section class="mt-5">
        <h2 class="text-center mb-4">Package Comparison</h2>
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-primary">
                    <tr>
                        <th>Feature</th>
                        <th class="text-center">Business Premium</th>
                        <th class="text-center">E-commerce</th>
                        <th class="text-center">Custom</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Domain & Hosting Setup</td>
                        <td class="text-center"><i class="fas fa-check text-success"></i></td>
                        <td class="text-center"><i class="fas fa-check text-success"></i></td>
                        <td class="text-center"><i class="fas fa-check text-success"></i></td>
                    </tr>
                    <tr>
                        <td>Multi-page Website</td>
                        <td class="text-center"><i class="fas fa-check text-success"></i></td>
                        <td class="text-center"><i class="fas fa-check text-success"></i></td>
                        <td class="text-center"><i class="fas fa-check text-success"></i></td>
                    </tr>
                    <tr>
                        <td>User Authentication</td>
                        <td class="text-center"><i class="fas fa-check text-success"></i></td>
                        <td class="text-center"><i class="fas fa-check text-success"></i></td>
                        <td class="text-center">Optional</td>
                    </tr>
                    <tr>
                        <td>E-commerce Features</td>
                        <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                        <td class="text-center"><i class="fas fa-check text-success"></i></td>
                        <td class="text-center">Optional</td>
                    </tr>
                    <tr>
                        <td>Payment Gateway</td>
                        <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                        <td class="text-center"><i class="fas fa-check text-success"></i></td>
                        <td class="text-center">Optional</td>
                    </tr>
                    <tr>
                        <td>Multi-vendor Support</td>
                        <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                        <td class="text-center"><i class="fas fa-check text-success"></i></td>
                        <td class="text-center">Optional</td>
                    </tr>
                    <tr>
                        <td>Email Automation</td>
                        <td class="text-center">500/month</td>
                        <td class="text-center">500/month</td>
                        <td class="text-center">Optional</td>
                    </tr>
                    <tr>
                        <td>SEO Optimization</td>
                        <td class="text-center"><i class="fas fa-check text-success"></i></td>
                        <td class="text-center"><i class="fas fa-check text-success"></i></td>
                        <td class="text-center"><i class="fas fa-check text-success"></i></td>
                    </tr>
                    <tr>
                        <td>Google Analytics</td>
                        <td class="text-center"><i class="fas fa-check text-success"></i></td>
                        <td class="text-center"><i class="fas fa-check text-success"></i></td>
                        <td class="text-center"><i class="fas fa-check text-success"></i></td>
                    </tr>
                    <tr class="table-warning">
                        <td><strong>Price</strong></td>
                        <td class="text-center"><strong>₹15,000</strong></td>
                        <td class="text-center"><strong>₹75,000</strong></td>
                        <td class="text-center"><strong>Custom Quote</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </section>
    
    <!-- FAQ Section -->
    <section class="mt-5">
        <h2 class="text-center mb-4">Frequently Asked Questions</h2>
        <div class="accordion" id="faqAccordion">
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                        What is included in the hosting setup?
                    </button>
                </h2>
                <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                    <div class="accordion-body">
                        We provide complete domain registration and hosting setup including SSL certificate, email accounts, and technical configuration. The hosting is optimized for performance and security.
                    </div>
                </div>
            </div>
            
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                        How long does it take to complete a project?
                    </button>
                </h2>
                <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                    <div class="accordion-body">
                        Business Premium Package: 7-10 days<br>
                        E-commerce Package: 15-20 days<br>
                        Custom Development: Depends on requirements (discussed during consultation)
                    </div>
                </div>
            </div>
            
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                        Do you provide ongoing support?
                    </button>
                </h2>
                <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                    <div class="accordion-body">
                        Yes, we provide 3 months of free support after project completion. Extended support packages are available at affordable rates.
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<?php require_once 'includes/footer.php'; ?>
