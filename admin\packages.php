<?php
$page_title = __('manage_packages');
require_once '../includes/header.php';

// Require admin role
$auth->requireRole('admin');

$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add' || $action === 'edit') {
        $id = (int)($_POST['id'] ?? 0);
        $name_en = sanitize($_POST['name_en'] ?? '');
        $name_hi = sanitize($_POST['name_hi'] ?? '');
        $description_en = sanitize($_POST['description_en'] ?? '');
        $description_hi = sanitize($_POST['description_hi'] ?? '');
        $price = (float)($_POST['price'] ?? 0);
        $package_type = sanitize($_POST['package_type'] ?? '');
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        
        // Process features
        $features_en = array_filter(array_map('trim', explode("\n", $_POST['features_en'] ?? '')));
        $features_hi = array_filter(array_map('trim', explode("\n", $_POST['features_hi'] ?? '')));
        
        if (empty($name_en) || empty($description_en) || empty($package_type)) {
            $error = 'Please fill all required fields';
        } else {
            $packageData = [
                'name_en' => $name_en,
                'name_hi' => $name_hi,
                'description_en' => $description_en,
                'description_hi' => $description_hi,
                'price' => $price,
                'package_type' => $package_type,
                'features_en' => json_encode($features_en),
                'features_hi' => json_encode($features_hi),
                'is_active' => $is_active
            ];
            
            try {
                if ($action === 'add') {
                    $package_id = $db->insert('service_packages', $packageData);
                    if ($package_id) {
                        $success = 'Package added successfully';
                        logActivity("Package added: $name_en");
                    } else {
                        $error = 'Failed to add package';
                    }
                } else {
                    $result = $db->update('service_packages', $packageData, 'id = ?', [$id]);
                    if ($result) {
                        $success = 'Package updated successfully';
                        logActivity("Package updated: $name_en (ID: $id)");
                    } else {
                        $error = 'Failed to update package';
                    }
                }
            } catch (Exception $e) {
                logActivity("Package operation error: " . $e->getMessage(), 'error');
                $error = 'An error occurred while saving the package';
            }
        }
    } elseif ($action === 'delete') {
        $id = (int)($_POST['id'] ?? 0);
        if ($id) {
            try {
                // Check if package has orders
                $orderCount = $db->fetch('SELECT COUNT(*) as count FROM orders WHERE package_id = ?', [$id])['count'];
                
                if ($orderCount > 0) {
                    $error = 'Cannot delete package with existing orders. Deactivate it instead.';
                } else {
                    $result = $db->delete('service_packages', 'id = ?', [$id]);
                    if ($result) {
                        $success = 'Package deleted successfully';
                        logActivity("Package deleted: ID $id");
                    } else {
                        $error = 'Failed to delete package';
                    }
                }
            } catch (Exception $e) {
                logActivity("Package deletion error: " . $e->getMessage(), 'error');
                $error = 'An error occurred while deleting the package';
            }
        }
    }
}

// Get all packages
$packages = $db->fetchAll('SELECT * FROM service_packages ORDER BY id');

// Get package for editing
$edit_package = null;
if (isset($_GET['edit'])) {
    $edit_id = (int)$_GET['edit'];
    $edit_package = $db->fetch('SELECT * FROM service_packages WHERE id = ?', [$edit_id]);
}
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-box me-2 text-primary"></i>
                        <?php echo __('manage_packages'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Package Name</th>
                                    <th>Type</th>
                                    <th>Price</th>
                                    <th>Status</th>
                                    <th>Orders</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($packages as $package): ?>
                                    <?php
                                    $orderCount = $db->fetch('SELECT COUNT(*) as count FROM orders WHERE package_id = ?', [$package['id']])['count'];
                                    ?>
                                    <tr>
                                        <td><?php echo $package['id']; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($package['name_en']); ?></strong><br>
                                            <small class="text-muted"><?php echo htmlspecialchars($package['name_hi']); ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                <?php echo ucfirst($package['package_type']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($package['price'] > 0): ?>
                                                <?php echo formatCurrency($package['price']); ?>
                                            <?php else: ?>
                                                <span class="text-muted">Custom</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $package['is_active'] ? 'success' : 'danger'; ?>">
                                                <?php echo $package['is_active'] ? 'Active' : 'Inactive'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $orderCount; ?></span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="?edit=<?php echo $package['id']; ?>" class="btn btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if ($orderCount == 0): ?>
                                                    <button type="button" class="btn btn-outline-danger" 
                                                            onclick="deletePackage(<?php echo $package['id']; ?>)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-plus me-2 text-primary"></i>
                        <?php echo $edit_package ? 'Edit Package' : 'Add New Package'; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <input type="hidden" name="action" value="<?php echo $edit_package ? 'edit' : 'add'; ?>">
                        <?php if ($edit_package): ?>
                            <input type="hidden" name="id" value="<?php echo $edit_package['id']; ?>">
                        <?php endif; ?>
                        
                        <div class="mb-3">
                            <label for="name_en" class="form-label">Package Name (English) *</label>
                            <input type="text" class="form-control" id="name_en" name="name_en" 
                                   value="<?php echo htmlspecialchars($edit_package['name_en'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="name_hi" class="form-label">Package Name (Hindi)</label>
                            <input type="text" class="form-control" id="name_hi" name="name_hi" 
                                   value="<?php echo htmlspecialchars($edit_package['name_hi'] ?? ''); ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="package_type" class="form-label">Package Type *</label>
                            <select class="form-control" id="package_type" name="package_type" required>
                                <option value="">Select Type</option>
                                <option value="business" <?php echo ($edit_package['package_type'] ?? '') === 'business' ? 'selected' : ''; ?>>
                                    Business
                                </option>
                                <option value="ecommerce" <?php echo ($edit_package['package_type'] ?? '') === 'ecommerce' ? 'selected' : ''; ?>>
                                    E-commerce
                                </option>
                                <option value="custom" <?php echo ($edit_package['package_type'] ?? '') === 'custom' ? 'selected' : ''; ?>>
                                    Custom
                                </option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="price" class="form-label">Price (₹)</label>
                            <input type="number" class="form-control" id="price" name="price" step="0.01" min="0"
                                   value="<?php echo $edit_package['price'] ?? '0'; ?>">
                            <small class="text-muted">Set to 0 for custom pricing</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description_en" class="form-label">Description (English) *</label>
                            <textarea class="form-control" id="description_en" name="description_en" rows="3" required><?php echo htmlspecialchars($edit_package['description_en'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description_hi" class="form-label">Description (Hindi)</label>
                            <textarea class="form-control" id="description_hi" name="description_hi" rows="3"><?php echo htmlspecialchars($edit_package['description_hi'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="features_en" class="form-label">Features (English) *</label>
                            <textarea class="form-control" id="features_en" name="features_en" rows="5" required 
                                      placeholder="One feature per line"><?php 
                                if ($edit_package) {
                                    $features = json_decode($edit_package['features_en'], true);
                                    echo htmlspecialchars(implode("\n", $features));
                                }
                            ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="features_hi" class="form-label">Features (Hindi)</label>
                            <textarea class="form-control" id="features_hi" name="features_hi" rows="5" 
                                      placeholder="One feature per line"><?php 
                                if ($edit_package) {
                                    $features = json_decode($edit_package['features_hi'], true);
                                    echo htmlspecialchars(implode("\n", $features));
                                }
                            ?></textarea>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" 
                                   <?php echo ($edit_package['is_active'] ?? 1) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_active">
                                Active Package
                            </label>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                <?php echo $edit_package ? 'Update Package' : 'Add Package'; ?>
                            </button>
                            
                            <?php if ($edit_package): ?>
                                <a href="<?php echo SITE_URL; ?>/admin/packages.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this package? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="" style="display: inline;">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" id="deletePackageId">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deletePackage(id) {
    document.getElementById('deletePackageId').value = id;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<?php require_once '../includes/footer.php'; ?>
