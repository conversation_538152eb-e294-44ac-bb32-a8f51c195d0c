<?php
/**
 * Database Setup Script
 * Run this file to create the database and tables
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Database Setup</h2>";

try {
    // Connect to MySQL server (without database)
    $pdo = new PDO("mysql:host=localhost", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✓ Connected to MySQL server<br>";
    
    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS websitedeveloper0002 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✓ Database 'websitedeveloper0002' created<br>";
    
    // Select the database
    $pdo->exec("USE websitedeveloper0002");
    echo "✓ Using database 'websitedeveloper0002'<br>";
    
    // Create users table
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        role ENUM('admin', 'customer', 'vendor') DEFAULT 'customer',
        status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
        email_verified BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "✓ Users table created<br>";
    
    // Create service_packages table
    $sql = "CREATE TABLE IF NOT EXISTS service_packages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name_en VARCHAR(100) NOT NULL,
        name_hi VARCHAR(100) NOT NULL,
        description_en TEXT NOT NULL,
        description_hi TEXT NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) DEFAULT 'INR',
        features_en JSON NOT NULL,
        features_hi JSON NOT NULL,
        package_type ENUM('business', 'ecommerce', 'custom') NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "✓ Service packages table created<br>";
    
    // Insert sample packages
    $packages = [
        [
            'name_en' => 'Business Premium Package',
            'name_hi' => 'बिजनेस प्रीमियम पैकेज',
            'description_en' => 'Complete business website with all essential features',
            'description_hi' => 'सभी आवश्यक सुविधाओं के साथ पूर्ण व्यावसायिक वेबसाइट',
            'price' => 15000,
            'features_en' => '["Free Domain & Hosting Setup", "Multi-page website", "Secure authentication", "Query Dashboard", "Essential SEO"]',
            'features_hi' => '["मुफ्त डोमेन और होस्टिंग सेटअप", "मल्टी-पेज वेबसाइट", "सुरक्षित प्रमाणीकरण", "क्वेरी डैशबोर्ड", "आवश्यक SEO"]',
            'package_type' => 'business'
        ],
        [
            'name_en' => 'E-commerce Package',
            'name_hi' => 'ई-कॉमर्स पैकेज',
            'description_en' => 'Complete online store with payment gateway and inventory management',
            'description_hi' => 'पेमेंट गेटवे और इन्वेंटरी प्रबंधन के साथ पूर्ण ऑनलाइन स्टोर',
            'price' => 75000,
            'features_en' => '["Complete online store", "Payment gateway integration", "Inventory management", "Order tracking", "Advanced SEO"]',
            'features_hi' => '["पूर्ण ऑनलाइन स्टोर", "पेमेंट गेटवे एकीकरण", "इन्वेंटरी प्रबंधन", "ऑर्डर ट्रैकिंग", "उन्नत SEO"]',
            'package_type' => 'ecommerce'
        ],
        [
            'name_en' => 'Custom Development',
            'name_hi' => 'कस्टम डेवलपमेंट',
            'description_en' => 'Custom solutions tailored to your specific requirements',
            'description_hi' => 'आपकी विशिष्ट आवश्यकताओं के अनुरूप कस्टम समाधान',
            'price' => 0,
            'features_en' => '["Custom solutions", "CRM, booking systems", "Modular pricing", "Google Analytics", "Requirement-based scoping"]',
            'features_hi' => '["कस्टम समाधान", "CRM, बुकिंग सिस्टम", "मॉड्यूलर मूल्य निर्धारण", "Google Analytics", "आवश्यकता-आधारित स्कोपिंग"]',
            'package_type' => 'custom'
        ]
    ];
    
    foreach ($packages as $package) {
        $stmt = $pdo->prepare("INSERT IGNORE INTO service_packages (name_en, name_hi, description_en, description_hi, price, features_en, features_hi, package_type) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $package['name_en'],
            $package['name_hi'],
            $package['description_en'],
            $package['description_hi'],
            $package['price'],
            $package['features_en'],
            $package['features_hi'],
            $package['package_type']
        ]);
    }
    echo "✓ Sample packages inserted<br>";
    
    // Create admin user
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, email, password, full_name, role) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute(['admin', '<EMAIL>', $admin_password, 'Administrator', 'admin']);
    echo "✓ Admin user created (username: admin, password: admin123)<br>";
    
    echo "<br><strong>Database setup completed successfully!</strong><br>";
    echo "<a href='simple-test.php'>Test the website</a> | <a href='debug.php'>Run debug</a>";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}
?>
