<?php
$page_title = __('payment');
require_once 'includes/header.php';

// Require login
$auth->requireLogin();

$user = $auth->getCurrentUser();
$error = '';
$success = '';

// Get order details
$order_id = (int)($_GET['order'] ?? 0);
if (!$order_id) {
    header('Location: ' . SITE_URL . '/orders.php');
    exit;
}

$order = $db->fetch(
    'SELECT o.*, sp.name_en, sp.name_hi, sp.package_type 
     FROM orders o 
     JOIN service_packages sp ON o.package_id = sp.id 
     WHERE o.id = ? AND o.user_id = ?',
    [$order_id, $user['id']]
);

if (!$order) {
    header('Location: ' . SITE_URL . '/orders.php');
    exit;
}

// Check if order is payable
if ($order['payment_status'] === 'paid' || $order['status'] === 'cancelled' || $order['total_amount'] <= 0) {
    header('Location: ' . SITE_URL . '/order-details.php?id=' . $order_id);
    exit;
}

// Handle payment processing
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create_payment') {
        try {
            // Create Razorpay order
            $razorpay_order_id = createRazorpayOrder($order);
            
            if ($razorpay_order_id) {
                // Update order with Razorpay order ID
                $db->update('orders', ['razorpay_order_id' => $razorpay_order_id], 'id = ?', [$order_id]);
                $order['razorpay_order_id'] = $razorpay_order_id;
                
                logActivity("Razorpay order created: {$razorpay_order_id} for order {$order['order_number']}");
            } else {
                $error = 'Failed to create payment order. Please try again.';
            }
        } catch (Exception $e) {
            logActivity("Payment creation error: " . $e->getMessage(), 'error');
            $error = 'An error occurred while creating payment. Please try again.';
        }
    } elseif ($action === 'verify_payment') {
        $razorpay_payment_id = sanitize($_POST['razorpay_payment_id'] ?? '');
        $razorpay_order_id = sanitize($_POST['razorpay_order_id'] ?? '');
        $razorpay_signature = sanitize($_POST['razorpay_signature'] ?? '');
        
        if ($razorpay_payment_id && $razorpay_order_id && $razorpay_signature) {
            $verification_result = verifyRazorpayPayment($razorpay_payment_id, $razorpay_order_id, $razorpay_signature);
            
            if ($verification_result['success']) {
                // Update order status
                $updateData = [
                    'payment_status' => 'paid',
                    'status' => 'paid',
                    'razorpay_payment_id' => $razorpay_payment_id,
                    'paid_at' => date('Y-m-d H:i:s')
                ];
                
                $db->update('orders', $updateData, 'id = ?', [$order_id]);
                
                logActivity("Payment verified for order: {$order['order_number']} - Payment ID: {$razorpay_payment_id}");
                
                // Send confirmation email
                sendOrderConfirmationEmail($order, $user);
                
                // Redirect to success page
                header('Location: ' . SITE_URL . '/payment-success.php?order=' . $order_id);
                exit;
            } else {
                $error = 'Payment verification failed. Please contact support.';
                logActivity("Payment verification failed for order: {$order['order_number']} - " . $verification_result['message'], 'error');
            }
        } else {
            $error = 'Invalid payment data received.';
        }
    }
}

// Calculate amounts
$subtotal = $order['total_amount'];
$tax_amount = $order['tax_amount'] ?? 0;
$total_amount = $subtotal + $tax_amount;
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-credit-card me-2 text-primary"></i>
                        <?php echo __('payment'); ?>
                    </h3>
                </div>
                <div class="card-body p-4">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Order Summary -->
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="mb-3">Order Summary</h5>
                            
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Order Number:</span>
                                        <strong><?php echo $order['order_number']; ?></strong>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Package:</span>
                                        <span><?php echo getCurrentLanguage() === 'hi' ? $order['name_hi'] : $order['name_en']; ?></span>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Type:</span>
                                        <span class="badge bg-info"><?php echo ucfirst($order['package_type']); ?></span>
                                    </div>
                                    
                                    <hr>
                                    
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Subtotal:</span>
                                        <span><?php echo formatCurrency($subtotal); ?></span>
                                    </div>
                                    
                                    <?php if ($tax_amount > 0): ?>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Tax (18% GST):</span>
                                        <span><?php echo formatCurrency($tax_amount); ?></span>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <hr>
                                    
                                    <div class="d-flex justify-content-between">
                                        <strong>Total Amount:</strong>
                                        <strong class="text-primary"><?php echo formatCurrency($total_amount); ?></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h5 class="mb-3">Payment Method</h5>
                            
                            <div class="card">
                                <div class="card-body text-center">
                                    <img src="<?php echo SITE_URL; ?>/assets/images/razorpay-logo.png" 
                                         alt="Razorpay" class="mb-3" style="max-height: 40px;">
                                    
                                    <p class="text-muted mb-3">
                                        Secure payment powered by Razorpay.<br>
                                        We accept all major credit cards, debit cards, UPI, and net banking.
                                    </p>
                                    
                                    <div class="payment-methods mb-3">
                                        <i class="fab fa-cc-visa text-primary me-2" style="font-size: 1.5rem;"></i>
                                        <i class="fab fa-cc-mastercard text-warning me-2" style="font-size: 1.5rem;"></i>
                                        <i class="fas fa-university text-info me-2" style="font-size: 1.5rem;"></i>
                                        <i class="fas fa-mobile-alt text-success" style="font-size: 1.5rem;"></i>
                                    </div>
                                    
                                    <?php if (!isset($order['razorpay_order_id']) || empty($order['razorpay_order_id'])): ?>
                                        <form method="POST" action="">
                                            <input type="hidden" name="action" value="create_payment">
                                            <button type="submit" class="btn btn-primary btn-lg">
                                                <i class="fas fa-lock me-2"></i>
                                                Proceed to Pay <?php echo formatCurrency($total_amount); ?>
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <button id="rzp-button" class="btn btn-success btn-lg">
                                            <i class="fas fa-credit-card me-2"></i>
                                            Pay Now <?php echo formatCurrency($total_amount); ?>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Security Information -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-shield-alt me-2"></i>Secure Payment</h6>
                                <ul class="mb-0">
                                    <li>Your payment information is encrypted and secure</li>
                                    <li>We do not store your card details</li>
                                    <li>All transactions are processed through Razorpay's secure gateway</li>
                                    <li>You will receive an email confirmation after successful payment</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Order Details -->
                    <?php if ($order['requirements']): ?>
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6>Project Requirements:</h6>
                            <div class="border p-3 rounded bg-light">
                                <?php echo nl2br(htmlspecialchars($order['requirements'])); ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="text-center mt-4">
                        <a href="<?php echo SITE_URL; ?>/order-details.php?id=<?php echo $order_id; ?>" 
                           class="btn btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Order
                        </a>
                        
                        <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-outline-info">
                            <i class="fas fa-question-circle me-2"></i>Need Help?
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if (isset($order['razorpay_order_id']) && !empty($order['razorpay_order_id'])): ?>
<!-- Razorpay Payment Script -->
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
<script>
document.getElementById('rzp-button').onclick = function(e) {
    e.preventDefault();
    
    var options = {
        "key": "<?php echo RAZORPAY_KEY_ID; ?>",
        "amount": <?php echo $total_amount * 100; ?>, // Amount in paise
        "currency": "INR",
        "name": "<?php echo SITE_NAME; ?>",
        "description": "<?php echo getCurrentLanguage() === 'hi' ? $order['name_hi'] : $order['name_en']; ?>",
        "image": "<?php echo SITE_URL; ?>/assets/images/logo.png",
        "order_id": "<?php echo $order['razorpay_order_id']; ?>",
        "handler": function (response) {
            // Create form to submit payment details
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = '';
            
            var actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'verify_payment';
            form.appendChild(actionInput);
            
            var paymentIdInput = document.createElement('input');
            paymentIdInput.type = 'hidden';
            paymentIdInput.name = 'razorpay_payment_id';
            paymentIdInput.value = response.razorpay_payment_id;
            form.appendChild(paymentIdInput);
            
            var orderIdInput = document.createElement('input');
            orderIdInput.type = 'hidden';
            orderIdInput.name = 'razorpay_order_id';
            orderIdInput.value = response.razorpay_order_id;
            form.appendChild(orderIdInput);
            
            var signatureInput = document.createElement('input');
            signatureInput.type = 'hidden';
            signatureInput.name = 'razorpay_signature';
            signatureInput.value = response.razorpay_signature;
            form.appendChild(signatureInput);
            
            document.body.appendChild(form);
            form.submit();
        },
        "prefill": {
            "name": "<?php echo htmlspecialchars($user['full_name']); ?>",
            "email": "<?php echo htmlspecialchars($user['email']); ?>",
            "contact": "<?php echo htmlspecialchars($user['phone'] ?? ''); ?>"
        },
        "notes": {
            "order_number": "<?php echo $order['order_number']; ?>",
            "user_id": "<?php echo $user['id']; ?>"
        },
        "theme": {
            "color": "#007bff"
        },
        "modal": {
            "ondismiss": function() {
                console.log('Payment modal closed');
            }
        }
    };
    
    var rzp = new Razorpay(options);
    rzp.on('payment.failed', function (response) {
        alert('Payment failed: ' + response.error.description);
        console.error('Payment failed:', response.error);
    });
    
    rzp.open();
};
</script>
<?php endif; ?>

<?php require_once 'includes/footer.php'; ?>
