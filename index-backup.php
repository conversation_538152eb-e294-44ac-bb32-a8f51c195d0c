<?php
$page_title = __('home');
$page_description = 'Professional web development services in India. Business websites, e-commerce solutions, and custom development with premium features.';
require_once 'includes/header.php';

// Get service packages from database
$packages = $db->fetchAll('SELECT * FROM service_packages WHERE is_active = 1 ORDER BY id');
?>

<!-- Hero Section -->
<section class="hero-section bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    <?php echo __('hero_title'); ?>
                </h1>
                <p class="lead mb-4">
                    <?php echo __('hero_subtitle'); ?>
                </p>
                <div class="d-flex flex-wrap gap-3">
                    <a href="#packages" class="btn btn-light btn-lg">
                        <i class="fas fa-eye me-2"></i><?php echo __('view_packages'); ?>
                    </a>
                    <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-phone me-2"></i><?php echo __('contact_us'); ?>
                    </a>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <img src="<?php echo SITE_URL; ?>/assets/images/hero-illustration.svg" 
                     alt="Web Development" class="img-fluid" style="max-height: 400px;">
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="display-5 fw-bold mb-3"><?php echo __('why_choose_us'); ?></h2>
                <p class="lead text-muted"><?php echo __('why_choose_us_subtitle'); ?></p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-0 shadow-custom">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-mobile-alt text-primary" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title"><?php echo __('responsive_design'); ?></h5>
                        <p class="card-text text-muted"><?php echo __('responsive_design_desc'); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-0 shadow-custom">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-shield-alt text-success" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title"><?php echo __('secure_development'); ?></h5>
                        <p class="card-text text-muted"><?php echo __('secure_development_desc'); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-0 shadow-custom">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-rocket text-warning" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title"><?php echo __('fast_delivery'); ?></h5>
                        <p class="card-text text-muted"><?php echo __('fast_delivery_desc'); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-0 shadow-custom">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-search text-info" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title"><?php echo __('seo_optimized'); ?></h5>
                        <p class="card-text text-muted"><?php echo __('seo_optimized_desc'); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-0 shadow-custom">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-headset text-danger" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title"><?php echo __('support_247'); ?></h5>
                        <p class="card-text text-muted"><?php echo __('support_247_desc'); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-0 shadow-custom">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-rupee-sign text-secondary" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title"><?php echo __('affordable_pricing'); ?></h5>
                        <p class="card-text text-muted"><?php echo __('affordable_pricing_desc'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Service Packages Section -->
<section id="packages" class="py-5 bg-light">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="display-5 fw-bold mb-3"><?php echo __('our_packages'); ?></h2>
                <p class="lead text-muted"><?php echo __('our_packages_subtitle'); ?></p>
            </div>
        </div>
        
        <div class="row">
            <?php foreach ($packages as $package): ?>
                <?php
                $features = json_decode($package['features_' . getCurrentLanguage()], true);
                $name = $package['name_' . getCurrentLanguage()];
                $description = $package['description_' . getCurrentLanguage()];
                ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 shadow-custom border-radius-custom <?php echo $package['package_type'] === 'ecommerce' ? 'border-primary' : ''; ?>">
                        <?php if ($package['package_type'] === 'ecommerce'): ?>
                            <div class="card-header bg-primary text-white text-center">
                                <span class="badge bg-light text-primary"><?php echo __('most_popular'); ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <div class="card-body text-center p-4">
                            <h4 class="card-title mb-3"><?php echo htmlspecialchars($name); ?></h4>
                            
                            <div class="price-section mb-4">
                                <?php if ($package['price'] > 0): ?>
                                    <h2 class="text-primary mb-0">
                                        <?php echo formatCurrency($package['price']); ?>
                                    </h2>
                                    <small class="text-muted"><?php echo __('one_time_payment'); ?></small>
                                <?php else: ?>
                                    <h2 class="text-info mb-0"><?php echo __('price_on_request'); ?></h2>
                                    <small class="text-muted"><?php echo __('custom_pricing'); ?></small>
                                <?php endif; ?>
                            </div>
                            
                            <p class="text-muted mb-4"><?php echo htmlspecialchars($description); ?></p>
                            
                            <ul class="list-unstyled text-start mb-4">
                                <?php foreach ($features as $feature): ?>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        <?php echo htmlspecialchars($feature); ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                            
                            <div class="d-grid">
                                <?php if ($package['price'] > 0): ?>
                                    <a href="<?php echo SITE_URL; ?>/order.php?package=<?php echo $package['id']; ?>" 
                                       class="btn btn-<?php echo $package['package_type'] === 'ecommerce' ? 'primary' : 'outline-primary'; ?> btn-lg">
                                        <i class="fas fa-shopping-cart me-2"></i><?php echo __('order_now'); ?>
                                    </a>
                                <?php else: ?>
                                    <a href="<?php echo SITE_URL; ?>/contact.php?package=<?php echo $package['package_type']; ?>" 
                                       class="btn btn-info btn-lg">
                                        <i class="fas fa-envelope me-2"></i><?php echo __('get_quote'); ?>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="display-5 fw-bold mb-3"><?php echo __('client_testimonials'); ?></h2>
                <p class="lead text-muted"><?php echo __('client_testimonials_subtitle'); ?></p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-0 shadow-custom">
                    <div class="card-body p-4">
                        <div class="mb-3">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <p class="card-text">"<?php echo __('testimonial_1'); ?>"</p>
                        <div class="d-flex align-items-center">
                            <div class="avatar me-3">
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-0">Rajesh Kumar</h6>
                                <small class="text-muted">Business Owner</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-0 shadow-custom">
                    <div class="card-body p-4">
                        <div class="mb-3">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <p class="card-text">"<?php echo __('testimonial_2'); ?>"</p>
                        <div class="d-flex align-items-center">
                            <div class="avatar me-3">
                                <div class="bg-success rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-0">Priya Sharma</h6>
                                <small class="text-muted">E-commerce Store Owner</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-0 shadow-custom">
                    <div class="card-body p-4">
                        <div class="mb-3">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <p class="card-text">"<?php echo __('testimonial_3'); ?>"</p>
                        <div class="d-flex align-items-center">
                            <div class="avatar me-3">
                                <div class="bg-info rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-0">Amit Patel</h6>
                                <small class="text-muted">Startup Founder</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row text-center">
            <div class="col-12">
                <h2 class="display-5 fw-bold mb-3"><?php echo __('ready_to_start'); ?></h2>
                <p class="lead mb-4"><?php echo __('ready_to_start_subtitle'); ?></p>
                <div class="d-flex flex-wrap justify-content-center gap-3">
                    <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-light btn-lg">
                        <i class="fas fa-rocket me-2"></i><?php echo __('start_project'); ?>
                    </a>
                    <a href="tel:<?php echo SITE_PHONE; ?>" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-phone me-2"></i><?php echo __('call_now'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>
