# Apache Configuration for websitedeveloper0002.in
# Basic Security and Performance

# Enable Rewrite Engine
RewriteEngine On

# Hide sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf)$">
    Require all denied
</FilesMatch>

# Protect sensitive directories
RedirectMatch 403 ^/config/
RedirectMatch 403 ^/database/
RedirectMatch 403 ^/includes/

# Custom error pages (optional)
# ErrorDocument 404 /404.php
# ErrorDocument 403 /403.php
# ErrorDocument 500 /500.php

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Require all denied
</FilesMatch>

# Sitemap and robots.txt rewrite
RewriteRule ^sitemap\.xml$ sitemap.php [L]
RewriteRule ^robots\.txt$ robots.php [L]

# Basic security
Options -Indexes
AddDefaultCharset UTF-8
