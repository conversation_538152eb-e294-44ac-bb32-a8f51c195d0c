# Apache Configuration for websitedeveloper0002.in
# Security and Performance Optimizations

# Enable Rewrite Engine
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options DENY
    
    # Prevent MIME type sniffing
    Header set X-Content-Type-Options nosniff
    
    # XSS Protection
    Header set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Remove Server Information
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# Hide sensitive files and directories
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Protect config directory
<Directory "config">
    Order Allow,Deny
    Deny from all
</Directory>

# Protect database directory
<Directory "database">
    Order Allow,Deny
    Deny from all
</Directory>

# Protect includes directory from direct access
<Directory "includes">
    Order Allow,Deny
    Deny from all
</Directory>

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# URL Rewriting for SEO-friendly URLs
# Redirect www to non-www (optional)
# RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
# RewriteRule ^(.*)$ http://%1/$1 [R=301,L]

# Force HTTPS (uncomment for production with SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Custom error pages
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Limit file upload size (adjust as needed)
php_value upload_max_filesize 10M
php_value post_max_size 10M

# Disable server signature
ServerSignature Off

# Prevent access to version control directories
<DirectoryMatch "\.git">
    Order Allow,Deny
    Deny from all
</DirectoryMatch>

# SEO-friendly URLs (if needed)
# RewriteRule ^services/([^/]+)/?$ services.php?package=$1 [L,QSA]
# RewriteRule ^blog/([^/]+)/?$ blog.php?slug=$1 [L,QSA]

# Sitemap and robots.txt rewrite
RewriteRule ^sitemap\.xml$ sitemap.php [L]
RewriteRule ^robots\.txt$ robots.php [L]

# Prevent hotlinking of images (optional)
# RewriteCond %{HTTP_REFERER} !^$
# RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?websitedeveloper0002\.in [NC]
# RewriteRule \.(jpg|jpeg|png|gif)$ - [NC,F,L]

# Security: Prevent PHP execution in uploads directory
<Directory "assets/uploads">
    <FilesMatch "\.php$">
        Order Allow,Deny
        Deny from all
    </FilesMatch>
</Directory>

# Optimize .htaccess performance
Options -Indexes
Options -MultiViews

# Set default charset
AddDefaultCharset UTF-8

# MIME types for web fonts
<IfModule mod_mime.c>
    AddType application/vnd.ms-fontobject .eot
    AddType font/truetype .ttf
    AddType font/opentype .otf
    AddType application/x-font-woff .woff
    AddType application/font-woff2 .woff2
</IfModule>
