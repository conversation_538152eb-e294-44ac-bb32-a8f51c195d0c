<?php
/**
 * Security Functions
 * reCAPTCHA, CSRF, Input Validation, etc.
 */

require_once __DIR__ . '/../config/config.php';

/**
 * Verify Google reCAPTCHA
 */
function verifyRecaptcha($recaptcha_response) {
    if (empty($recaptcha_response)) {
        return false;
    }
    
    $secret_key = RECAPTCHA_SECRET_KEY;
    $verify_url = 'https://www.google.com/recaptcha/api/siteverify';
    
    $data = [
        'secret' => $secret_key,
        'response' => $recaptcha_response,
        'remoteip' => $_SERVER['REMOTE_ADDR'] ?? ''
    ];
    
    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents($verify_url, false, $context);
    
    if ($result === false) {
        logActivity("reCAPTCHA verification failed: Unable to connect to Google", 'error');
        return false;
    }
    
    $response = json_decode($result, true);
    
    if ($response['success']) {
        logActivity("reCAPTCHA verification successful");
        return true;
    } else {
        logActivity("reCAPTCHA verification failed: " . implode(', ', $response['error-codes'] ?? []), 'error');
        return false;
    }
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Validate CSRF token
 */
function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Get CSRF token HTML input
 */
function getCSRFTokenInput() {
    return '<input type="hidden" name="csrf_token" value="' . generateCSRFToken() . '">';
}

/**
 * Sanitize input data
 */
function sanitize($input) {
    if (is_array($input)) {
        return array_map('sanitize', $input);
    }
    
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email address
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate phone number (Indian format)
 */
function isValidPhone($phone) {
    // Remove all non-digit characters
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // Check if it's a valid Indian phone number
    return preg_match('/^[6-9]\d{9}$/', $phone) || preg_match('/^91[6-9]\d{9}$/', $phone);
}

/**
 * Validate URL
 */
function isValidURL($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

/**
 * Check password strength
 */
function checkPasswordStrength($password) {
    $score = 0;
    $feedback = [];
    
    // Length check
    if (strlen($password) >= 8) {
        $score += 1;
    } else {
        $feedback[] = 'Password should be at least 8 characters long';
    }
    
    // Uppercase letter
    if (preg_match('/[A-Z]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'Include at least one uppercase letter';
    }
    
    // Lowercase letter
    if (preg_match('/[a-z]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'Include at least one lowercase letter';
    }
    
    // Number
    if (preg_match('/[0-9]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'Include at least one number';
    }
    
    // Special character
    if (preg_match('/[^A-Za-z0-9]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'Include at least one special character';
    }
    
    $strength_levels = [
        0 => 'Very Weak',
        1 => 'Weak',
        2 => 'Fair',
        3 => 'Good',
        4 => 'Strong',
        5 => 'Very Strong'
    ];
    
    return [
        'score' => $score,
        'strength' => $strength_levels[$score],
        'feedback' => $feedback,
        'is_strong' => $score >= 3
    ];
}

/**
 * Rate limiting check
 */
function checkRateLimit($action, $identifier, $max_attempts = 5, $time_window = 300) {
    global $db;
    
    $key = $action . '_' . $identifier;
    $current_time = time();
    $window_start = $current_time - $time_window;
    
    // Clean old attempts
    $db->delete('rate_limits', 'created_at < ?', [date('Y-m-d H:i:s', $window_start)]);
    
    // Count current attempts
    $attempts = $db->fetch(
        'SELECT COUNT(*) as count FROM rate_limits WHERE rate_key = ? AND created_at >= ?',
        [$key, date('Y-m-d H:i:s', $window_start)]
    )['count'];
    
    if ($attempts >= $max_attempts) {
        logActivity("Rate limit exceeded for $action by $identifier", 'warning');
        return false;
    }
    
    // Record this attempt
    $db->insert('rate_limits', [
        'rate_key' => $key,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
        'created_at' => date('Y-m-d H:i:s')
    ]);
    
    return true;
}

/**
 * Check for suspicious activity
 */
function checkSuspiciousActivity($user_id = null) {
    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // Check for common bot patterns
    $bot_patterns = [
        '/bot/i',
        '/crawler/i',
        '/spider/i',
        '/scraper/i'
    ];
    
    foreach ($bot_patterns as $pattern) {
        if (preg_match($pattern, $user_agent)) {
            logActivity("Bot detected: $user_agent from $ip", 'warning');
            return true;
        }
    }
    
    // Check for rapid requests from same IP
    if (!checkRateLimit('page_request', $ip, 100, 60)) {
        return true;
    }
    
    return false;
}

/**
 * Encrypt sensitive data
 */
function encryptData($data) {
    $key = ENCRYPTION_KEY;
    $iv = random_bytes(16);
    $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
    return base64_encode($iv . $encrypted);
}

/**
 * Decrypt sensitive data
 */
function decryptData($encrypted_data) {
    $key = ENCRYPTION_KEY;
    $data = base64_decode($encrypted_data);
    $iv = substr($data, 0, 16);
    $encrypted = substr($data, 16);
    return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
}

/**
 * Generate secure random token
 */
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * Hash password securely
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Check if IP is blacklisted
 */
function isIPBlacklisted($ip) {
    global $db;
    
    $blacklisted = $db->fetch(
        'SELECT id FROM ip_blacklist WHERE ip_address = ? AND is_active = 1',
        [$ip]
    );
    
    return $blacklisted !== false;
}

/**
 * Add IP to blacklist
 */
function blacklistIP($ip, $reason = '') {
    global $db;
    
    $data = [
        'ip_address' => $ip,
        'reason' => $reason,
        'is_active' => 1,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    return $db->insert('ip_blacklist', $data);
}

/**
 * Security headers
 */
function setSecurityHeaders() {
    // Prevent clickjacking
    header('X-Frame-Options: DENY');
    
    // Prevent MIME type sniffing
    header('X-Content-Type-Options: nosniff');
    
    // XSS Protection
    header('X-XSS-Protection: 1; mode=block');
    
    // Referrer Policy
    header('Referrer-Policy: strict-origin-when-cross-origin');
    
    // Content Security Policy (basic)
    header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://www.google.com https://www.gstatic.com https://checkout.razorpay.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.razorpay.com;");
    
    // HSTS (only for HTTPS)
    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
        header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
    }
}

/**
 * Log security event
 */
function logSecurityEvent($event, $details = '', $severity = 'info') {
    global $db;
    
    $data = [
        'event_type' => $event,
        'details' => $details,
        'severity' => $severity,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    $db->insert('security_logs', $data);
    
    // Also log to activity log
    logActivity("Security Event: $event - $details", $severity);
}

/**
 * Check for SQL injection patterns
 */
function detectSQLInjection($input) {
    $patterns = [
        '/(\bunion\b.*\bselect\b)/i',
        '/(\bselect\b.*\bfrom\b)/i',
        '/(\binsert\b.*\binto\b)/i',
        '/(\bdelete\b.*\bfrom\b)/i',
        '/(\bdrop\b.*\btable\b)/i',
        '/(\bupdate\b.*\bset\b)/i',
        '/(\'|\"|;|--|\#|\*|\|)/i'
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $input)) {
            logSecurityEvent('sql_injection_attempt', "Input: $input", 'high');
            return true;
        }
    }
    
    return false;
}

/**
 * Check for XSS patterns
 */
function detectXSS($input) {
    $patterns = [
        '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi',
        '/javascript:/i',
        '/on\w+\s*=/i',
        '/<iframe/i',
        '/<object/i',
        '/<embed/i'
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $input)) {
            logSecurityEvent('xss_attempt', "Input: $input", 'high');
            return true;
        }
    }
    
    return false;
}

/**
 * Validate file upload
 */
function validateFileUpload($file, $allowed_types = null, $max_size = null) {
    $allowed_types = $allowed_types ?? ALLOWED_FILE_TYPES;
    $max_size = $max_size ?? UPLOAD_MAX_SIZE;
    
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return ['valid' => false, 'error' => 'No file uploaded'];
    }
    
    // Check file size
    if ($file['size'] > $max_size) {
        return ['valid' => false, 'error' => 'File size exceeds limit'];
    }
    
    // Check file extension
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, $allowed_types)) {
        return ['valid' => false, 'error' => 'File type not allowed'];
    }
    
    // Check MIME type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mime_type = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    $allowed_mimes = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'pdf' => 'application/pdf',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    if (isset($allowed_mimes[$extension]) && $mime_type !== $allowed_mimes[$extension]) {
        return ['valid' => false, 'error' => 'File type mismatch'];
    }
    
    return ['valid' => true, 'error' => null];
}

// Set security headers on every request
setSecurityHeaders();

// Check for suspicious activity
if (checkSuspiciousActivity()) {
    // You might want to show a captcha or block the request
    // For now, just log it
}

// Check if IP is blacklisted
$current_ip = $_SERVER['REMOTE_ADDR'] ?? '';
if (isIPBlacklisted($current_ip)) {
    http_response_code(403);
    die('Access denied');
}
?>
