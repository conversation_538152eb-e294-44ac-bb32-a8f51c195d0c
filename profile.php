<?php
$page_title = __('my_profile');
require_once 'includes/header.php';

// Require login
$auth->requireLogin();

$user = $auth->getCurrentUser();
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $full_name = sanitize($_POST['full_name'] ?? '');
        $email = sanitize($_POST['email'] ?? '');
        $phone = sanitize($_POST['phone'] ?? '');
        
        // Validation
        if (empty($full_name) || empty($email)) {
            $error = __('field_required');
        } elseif (!isValidEmail($email)) {
            $error = __('invalid_email');
        } elseif ($phone && !isValidPhone($phone)) {
            $error = __('invalid_phone');
        } else {
            // Check if email is already taken by another user
            $existingUser = $db->fetch('SELECT id FROM users WHERE email = ? AND id != ?', [$email, $user['id']]);
            
            if ($existingUser) {
                $error = 'Email is already taken by another user';
            } else {
                try {
                    $updateData = [
                        'full_name' => $full_name,
                        'email' => $email,
                        'phone' => $phone
                    ];
                    
                    $result = $db->update('users', $updateData, 'id = ?', [$user['id']]);
                    
                    if ($result) {
                        $success = 'Profile updated successfully';
                        logActivity("Profile updated for user: {$user['username']}");
                        
                        // Refresh user data
                        $user = $auth->getCurrentUser();
                    } else {
                        $error = 'Failed to update profile';
                    }
                } catch (Exception $e) {
                    logActivity("Profile update error: " . $e->getMessage(), 'error');
                    $error = 'An error occurred while updating profile';
                }
            }
        }
    } elseif ($action === 'change_password') {
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $error = __('field_required');
        } elseif (strlen($new_password) < 6) {
            $error = __('password_min_length');
        } elseif ($new_password !== $confirm_password) {
            $error = __('passwords_not_match');
        } else {
            $result = $auth->changePassword($user['id'], $current_password, $new_password);
            
            if ($result['success']) {
                $success = $result['message'];
            } else {
                $error = $result['message'];
            }
        }
    }
}
?>

<div class="container py-5">
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-user me-2 text-primary"></i>
                        <?php echo __('my_profile'); ?>
                    </h3>
                </div>
                <div class="card-body p-4">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Profile Information Form -->
                    <form method="POST" action="">
                        <input type="hidden" name="action" value="update_profile">
                        
                        <h5 class="mb-3">Personal Information</h5>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label"><?php echo __('full_name'); ?> *</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label"><?php echo __('username'); ?></label>
                                <input type="text" class="form-control" id="username" 
                                       value="<?php echo htmlspecialchars($user['username']); ?>" disabled>
                                <small class="text-muted">Username cannot be changed</small>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label"><?php echo __('email'); ?> *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($user['email']); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label"><?php echo __('phone'); ?></label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>" 
                                       placeholder="+91-**********">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Account Role</label>
                                <input type="text" class="form-control" 
                                       value="<?php echo ucfirst($user['role']); ?>" disabled>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Member Since</label>
                                <input type="text" class="form-control" 
                                       value="<?php echo formatDate($user['created_at']); ?>" disabled>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Profile
                        </button>
                    </form>
                    
                    <hr class="my-4">
                    
                    <!-- Change Password Form -->
                    <h5 class="mb-3">Change Password</h5>
                    
                    <form method="POST" action="" id="passwordForm">
                        <input type="hidden" name="action" value="change_password">
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="current_password" class="form-label">Current Password *</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="current_password" name="current_password" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                        <i class="fas fa-eye" id="toggleIcon1"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="new_password" class="form-label">New Password *</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="new_password" name="new_password" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                        <i class="fas fa-eye" id="toggleIcon2"></i>
                                    </button>
                                </div>
                                <small class="text-muted"><?php echo __('password_min_length'); ?></small>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="confirm_password" class="form-label">Confirm New Password *</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                        <i class="fas fa-eye" id="toggleIcon3"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-key me-2"></i>Change Password
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Account Statistics -->
            <div class="card shadow-custom border-radius-custom mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2 text-primary"></i>
                        Account Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <?php
                    $stats = [
                        'total_orders' => $db->fetch('SELECT COUNT(*) as count FROM orders WHERE user_id = ?', [$user['id']])['count'],
                        'completed_orders' => $db->fetch('SELECT COUNT(*) as count FROM orders WHERE user_id = ? AND status = "completed"', [$user['id']])['count'],
                        'total_spent' => $db->fetch('SELECT COALESCE(SUM(total_amount), 0) as total FROM orders WHERE user_id = ? AND payment_status = "paid"', [$user['id']])['total'],
                        'inquiries_sent' => $db->fetch('SELECT COUNT(*) as count FROM inquiries WHERE email = ?', [$user['email']])['count']
                    ];
                    ?>
                    
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-primary mb-1"><?php echo $stats['total_orders']; ?></h4>
                            <small class="text-muted">Total Orders</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-success mb-1"><?php echo $stats['completed_orders']; ?></h4>
                            <small class="text-muted">Completed</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info mb-1"><?php echo formatCurrency($stats['total_spent']); ?></h4>
                            <small class="text-muted">Total Spent</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning mb-1"><?php echo $stats['inquiries_sent']; ?></h4>
                            <small class="text-muted">Inquiries</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Account Security -->
            <div class="card shadow-custom border-radius-custom mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-shield-alt me-2 text-primary"></i>
                        Account Security
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <strong>Email Verification</strong><br>
                            <small class="text-muted">Verify your email address</small>
                        </div>
                        <span class="badge bg-<?php echo $user['email_verified'] ? 'success' : 'warning'; ?>">
                            <?php echo $user['email_verified'] ? 'Verified' : 'Pending'; ?>
                        </span>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <strong>Two-Factor Auth</strong><br>
                            <small class="text-muted">Add extra security</small>
                        </div>
                        <span class="badge bg-secondary">Coming Soon</span>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>Login Sessions</strong><br>
                            <small class="text-muted">Manage active sessions</small>
                        </div>
                        <button class="btn btn-outline-primary btn-sm">Manage</button>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card shadow-custom border-radius-custom">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2 text-primary"></i>
                        Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo SITE_URL; ?>/dashboard.php" class="btn btn-outline-primary">
                            <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                        </a>
                        <a href="<?php echo SITE_URL; ?>/orders.php" class="btn btn-outline-success">
                            <i class="fas fa-list me-2"></i>View My Orders
                        </a>
                        <a href="<?php echo SITE_URL; ?>/services.php" class="btn btn-outline-info">
                            <i class="fas fa-plus me-2"></i>Place New Order
                        </a>
                        <a href="<?php echo SITE_URL; ?>/contact.php" class="btn btn-outline-warning">
                            <i class="fas fa-envelope me-2"></i>Contact Support
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const iconId = fieldId === 'current_password' ? 'toggleIcon1' : 
                   fieldId === 'new_password' ? 'toggleIcon2' : 'toggleIcon3';
    const toggleIcon = document.getElementById(iconId);
    
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const passwordForm = document.getElementById('passwordForm');
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePasswords() {
        if (newPassword.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('<?php echo __('passwords_not_match'); ?>');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    newPassword.addEventListener('input', validatePasswords);
    confirmPassword.addEventListener('input', validatePasswords);
});
</script>

<?php require_once 'includes/footer.php'; ?>
