<?php
$page_title = __('order_history');
require_once 'includes/header.php';

// Require login
$auth->requireLogin();

$user = $auth->getCurrentUser();

// Pagination
$page = (int)($_GET['page'] ?? 1);
$limit = ITEMS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Get total count
$total_orders = $db->fetch('SELECT COUNT(*) as count FROM orders WHERE user_id = ?', [$user['id']])['count'];
$total_pages = ceil($total_orders / $limit);

// Get orders with pagination
$orders = $db->fetchAll(
    'SELECT o.*, sp.name_en, sp.name_hi, sp.package_type 
     FROM orders o 
     JOIN service_packages sp ON o.package_id = sp.id 
     WHERE o.user_id = ? 
     ORDER BY o.created_at DESC 
     LIMIT ? OFFSET ?',
    [$user['id'], $limit, $offset]
);

// Get order statistics
$stats = [
    'total' => $total_orders,
    'pending' => $db->fetch('SELECT COUNT(*) as count FROM orders WHERE user_id = ? AND status = "pending"', [$user['id']])['count'],
    'processing' => $db->fetch('SELECT COUNT(*) as count FROM orders WHERE user_id = ? AND status IN ("paid", "processing")', [$user['id']])['count'],
    'completed' => $db->fetch('SELECT COUNT(*) as count FROM orders WHERE user_id = ? AND status = "completed"', [$user['id']])['count'],
    'cancelled' => $db->fetch('SELECT COUNT(*) as count FROM orders WHERE user_id = ? AND status = "cancelled"', [$user['id']])['count']
];
?>

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1"><?php echo __('order_history'); ?></h2>
                    <p class="text-muted mb-0">Track and manage all your orders</p>
                </div>
                <a href="<?php echo SITE_URL; ?>/services.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>New Order
                </a>
            </div>
        </div>
    </div>
    
    <!-- Order Statistics -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card shadow-custom border-radius-custom h-100">
                <div class="card-body text-center">
                    <h4 class="text-primary mb-1"><?php echo $stats['total']; ?></h4>
                    <small class="text-muted">Total Orders</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card shadow-custom border-radius-custom h-100">
                <div class="card-body text-center">
                    <h4 class="text-warning mb-1"><?php echo $stats['pending']; ?></h4>
                    <small class="text-muted">Pending</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card shadow-custom border-radius-custom h-100">
                <div class="card-body text-center">
                    <h4 class="text-info mb-1"><?php echo $stats['processing']; ?></h4>
                    <small class="text-muted">Processing</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card shadow-custom border-radius-custom h-100">
                <div class="card-body text-center">
                    <h4 class="text-success mb-1"><?php echo $stats['completed']; ?></h4>
                    <small class="text-muted">Completed</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card shadow-custom border-radius-custom h-100">
                <div class="card-body text-center">
                    <h4 class="text-danger mb-1"><?php echo $stats['cancelled']; ?></h4>
                    <small class="text-muted">Cancelled</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card shadow-custom border-radius-custom h-100">
                <div class="card-body text-center">
                    <?php $total_spent = $db->fetch('SELECT COALESCE(SUM(total_amount), 0) as total FROM orders WHERE user_id = ? AND payment_status = "paid"', [$user['id']])['total']; ?>
                    <h4 class="text-dark mb-1"><?php echo formatCurrency($total_spent); ?></h4>
                    <small class="text-muted">Total Spent</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Orders List -->
    <div class="card shadow-custom border-radius-custom">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2 text-primary"></i>
                Your Orders
            </h5>
        </div>
        <div class="card-body">
            <?php if (empty($orders)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-shopping-cart text-muted" style="font-size: 4rem; opacity: 0.3;"></i>
                    <h4 class="text-muted mt-3">No orders found</h4>
                    <p class="text-muted">You haven't placed any orders yet. Start by exploring our services.</p>
                    <a href="<?php echo SITE_URL; ?>/services.php" class="btn btn-primary">
                        <i class="fas fa-eye me-2"></i>Browse Services
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th><?php echo __('order_number'); ?></th>
                                <th><?php echo __('package_name'); ?></th>
                                <th><?php echo __('amount'); ?></th>
                                <th><?php echo __('order_status'); ?></th>
                                <th><?php echo __('payment_status'); ?></th>
                                <th><?php echo __('order_date'); ?></th>
                                <th><?php echo __('actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($orders as $order): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo $order['order_number']; ?></strong>
                                    </td>
                                    <td>
                                        <div>
                                            <?php echo getCurrentLanguage() === 'hi' ? $order['name_hi'] : $order['name_en']; ?>
                                        </div>
                                        <small class="text-muted">
                                            <i class="fas fa-tag me-1"></i>
                                            <?php echo ucfirst($order['package_type']); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?php if ($order['total_amount'] > 0): ?>
                                            <strong><?php echo formatCurrency($order['total_amount']); ?></strong>
                                        <?php else: ?>
                                            <span class="text-muted">Custom Quote</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $statusClass = [
                                            'pending' => 'warning',
                                            'paid' => 'info',
                                            'processing' => 'primary',
                                            'completed' => 'success',
                                            'cancelled' => 'danger',
                                            'refunded' => 'secondary'
                                        ];
                                        $class = $statusClass[$order['status']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?php echo $class; ?>">
                                            <?php echo ucfirst($order['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $paymentClass = [
                                            'pending' => 'warning',
                                            'paid' => 'success',
                                            'failed' => 'danger',
                                            'refunded' => 'secondary'
                                        ];
                                        $class = $paymentClass[$order['payment_status']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?php echo $class; ?>">
                                            <?php echo ucfirst($order['payment_status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div><?php echo formatDate($order['created_at']); ?></div>
                                        <small class="text-muted"><?php echo timeAgo($order['created_at']); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo SITE_URL; ?>/order-details.php?id=<?php echo $order['id']; ?>" 
                                               class="btn btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            <?php if ($order['status'] === 'pending' && $order['total_amount'] > 0): ?>
                                                <a href="<?php echo SITE_URL; ?>/payment.php?order=<?php echo $order['id']; ?>" 
                                                   class="btn btn-outline-success" title="Pay Now">
                                                    <i class="fas fa-credit-card"></i>
                                                </a>
                                            <?php endif; ?>
                                            
                                            <?php if (in_array($order['status'], ['pending', 'paid']) && $order['payment_status'] !== 'paid'): ?>
                                                <button type="button" class="btn btn-outline-danger" 
                                                        onclick="cancelOrder(<?php echo $order['id']; ?>)" title="Cancel Order">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="mt-4">
                        <?php echo generatePagination($page, $total_pages, '/orders.php'); ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Cancel Order Modal -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Order</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to cancel this order?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Note:</strong> Once cancelled, this action cannot be undone. 
                    If you have already made a payment, please contact support for refund processing.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Keep Order</button>
                <button type="button" class="btn btn-danger" onclick="confirmCancelOrder()">Cancel Order</button>
            </div>
        </div>
    </div>
</div>

<script>
let orderToCancel = null;

function cancelOrder(orderId) {
    orderToCancel = orderId;
    new bootstrap.Modal(document.getElementById('cancelModal')).show();
}

function confirmCancelOrder() {
    if (orderToCancel) {
        // Create a form to submit the cancellation
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo SITE_URL; ?>/cancel-order.php';
        
        const orderIdInput = document.createElement('input');
        orderIdInput.type = 'hidden';
        orderIdInput.name = 'order_id';
        orderIdInput.value = orderToCancel;
        
        form.appendChild(orderIdInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php require_once 'includes/footer.php'; ?>
