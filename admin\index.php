<?php
$page_title = __('admin_dashboard');
require_once '../includes/header.php';

// Require admin role
$auth->requireRole('admin');

$user = $auth->getCurrentUser();

// Get admin statistics
$stats = [
    'total_users' => $db->fetch('SELECT COUNT(*) as count FROM users WHERE role != "admin"')['count'],
    'total_orders' => $db->fetch('SELECT COUNT(*) as count FROM orders')['count'],
    'pending_orders' => $db->fetch('SELECT COUNT(*) as count FROM orders WHERE status = "pending"')['count'],
    'total_revenue' => $db->fetch('SELECT COALESCE(SUM(total_amount), 0) as total FROM orders WHERE payment_status = "paid"')['total'],
    'new_inquiries' => $db->fetch('SELECT COUNT(*) as count FROM inquiries WHERE status = "new"')['count'],
    'active_orders' => $db->fetch('SELECT COUNT(*) as count FROM orders WHERE status IN ("paid", "processing")')['count']
];

// Get recent orders
$recent_orders = $db->fetchAll(
    'SELECT o.*, u.full_name, u.email, sp.name_en, sp.package_type 
     FROM orders o 
     JOIN users u ON o.user_id = u.id 
     JOIN service_packages sp ON o.package_id = sp.id 
     ORDER BY o.created_at DESC 
     LIMIT 10'
);

// Get recent inquiries
$recent_inquiries = $db->fetchAll(
    'SELECT * FROM inquiries ORDER BY created_at DESC LIMIT 5'
);

// Monthly revenue data for chart
$monthly_revenue = $db->fetchAll(
    'SELECT 
        DATE_FORMAT(created_at, "%Y-%m") as month,
        SUM(total_amount) as revenue,
        COUNT(*) as orders
     FROM orders 
     WHERE payment_status = "paid" 
       AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
     GROUP BY DATE_FORMAT(created_at, "%Y-%m")
     ORDER BY month'
);
?>

<div class="container-fluid py-4">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-dark text-white shadow-custom border-radius-custom">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">
                                <i class="fas fa-tachometer-alt me-3"></i>
                                <?php echo __('admin_dashboard'); ?>
                            </h2>
                            <p class="mb-0 opacity-75">
                                Welcome back, <?php echo htmlspecialchars($user['full_name']); ?>! 
                                Manage your website, orders, and customers from here.
                            </p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="text-muted">
                                <i class="fas fa-calendar me-2"></i>
                                <?php echo date('l, F j, Y'); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-2 col-lg-4 col-md-6 mb-3">
            <div class="card shadow-custom border-radius-custom h-100">
                <div class="card-body text-center">
                    <div class="service-icon mx-auto mb-3" style="width: 60px; height: 60px; font-size: 1.5rem;">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="text-primary mb-1"><?php echo $stats['total_users']; ?></h3>
                    <p class="text-muted mb-0 small"><?php echo __('total_users'); ?></p>
                </div>
            </div>
        </div>
        
        <div class="col-xl-2 col-lg-4 col-md-6 mb-3">
            <div class="card shadow-custom border-radius-custom h-100">
                <div class="card-body text-center">
                    <div class="service-icon mx-auto mb-3" style="width: 60px; height: 60px; font-size: 1.5rem; background: linear-gradient(135deg, #28a745, #20c997);">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h3 class="text-success mb-1"><?php echo $stats['total_orders']; ?></h3>
                    <p class="text-muted mb-0 small"><?php echo __('total_orders'); ?></p>
                </div>
            </div>
        </div>
        
        <div class="col-xl-2 col-lg-4 col-md-6 mb-3">
            <div class="card shadow-custom border-radius-custom h-100">
                <div class="card-body text-center">
                    <div class="service-icon mx-auto mb-3" style="width: 60px; height: 60px; font-size: 1.5rem; background: linear-gradient(135deg, #ffc107, #ff8c00);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="text-warning mb-1"><?php echo $stats['pending_orders']; ?></h3>
                    <p class="text-muted mb-0 small"><?php echo __('pending_orders'); ?></p>
                </div>
            </div>
        </div>
        
        <div class="col-xl-2 col-lg-4 col-md-6 mb-3">
            <div class="card shadow-custom border-radius-custom h-100">
                <div class="card-body text-center">
                    <div class="service-icon mx-auto mb-3" style="width: 60px; height: 60px; font-size: 1.5rem; background: linear-gradient(135deg, #6f42c1, #e83e8c);">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <h3 class="text-info mb-1"><?php echo formatCurrency($stats['total_revenue']); ?></h3>
                    <p class="text-muted mb-0 small"><?php echo __('total_revenue'); ?></p>
                </div>
            </div>
        </div>
        
        <div class="col-xl-2 col-lg-4 col-md-6 mb-3">
            <div class="card shadow-custom border-radius-custom h-100">
                <div class="card-body text-center">
                    <div class="service-icon mx-auto mb-3" style="width: 60px; height: 60px; font-size: 1.5rem; background: linear-gradient(135deg, #dc3545, #fd7e14);">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h3 class="text-danger mb-1"><?php echo $stats['new_inquiries']; ?></h3>
                    <p class="text-muted mb-0 small"><?php echo __('new_inquiries'); ?></p>
                </div>
            </div>
        </div>
        
        <div class="col-xl-2 col-lg-4 col-md-6 mb-3">
            <div class="card shadow-custom border-radius-custom h-100">
                <div class="card-body text-center">
                    <div class="service-icon mx-auto mb-3" style="width: 60px; height: 60px; font-size: 1.5rem; background: linear-gradient(135deg, #17a2b8, #6610f2);">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <h3 class="text-info mb-1"><?php echo $stats['active_orders']; ?></h3>
                    <p class="text-muted mb-0 small"><?php echo __('active_orders'); ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2 text-primary"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                            <a href="<?php echo SITE_URL; ?>/admin/orders.php" class="btn btn-outline-primary w-100">
                                <i class="fas fa-shopping-cart me-2"></i><?php echo __('manage_orders'); ?>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                            <a href="<?php echo SITE_URL; ?>/admin/users.php" class="btn btn-outline-success w-100">
                                <i class="fas fa-users me-2"></i><?php echo __('manage_users'); ?>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                            <a href="<?php echo SITE_URL; ?>/admin/inquiries.php" class="btn btn-outline-warning w-100">
                                <i class="fas fa-envelope me-2"></i><?php echo __('manage_inquiries'); ?>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                            <a href="<?php echo SITE_URL; ?>/admin/packages.php" class="btn btn-outline-info w-100">
                                <i class="fas fa-box me-2"></i><?php echo __('manage_packages'); ?>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                            <a href="<?php echo SITE_URL; ?>/admin/settings.php" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-cog me-2"></i><?php echo __('site_settings'); ?>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
                            <a href="<?php echo SITE_URL; ?>/admin/analytics.php" class="btn btn-outline-dark w-100">
                                <i class="fas fa-chart-bar me-2"></i><?php echo __('analytics'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Recent Orders -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2 text-primary"></i>
                        Recent Orders
                    </h5>
                    <a href="<?php echo SITE_URL; ?>/admin/orders.php" class="btn btn-outline-primary btn-sm">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Customer</th>
                                    <th>Package</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_orders as $order): ?>
                                    <tr>
                                        <td><strong><?php echo $order['order_number']; ?></strong></td>
                                        <td>
                                            <?php echo htmlspecialchars($order['full_name']); ?><br>
                                            <small class="text-muted"><?php echo htmlspecialchars($order['email']); ?></small>
                                        </td>
                                        <td><?php echo $order['name_en']; ?></td>
                                        <td>
                                            <?php if ($order['total_amount'] > 0): ?>
                                                <?php echo formatCurrency($order['total_amount']); ?>
                                            <?php else: ?>
                                                <span class="text-muted">Quote</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClass = [
                                                'pending' => 'warning',
                                                'paid' => 'info',
                                                'processing' => 'primary',
                                                'completed' => 'success',
                                                'cancelled' => 'danger'
                                            ];
                                            $class = $statusClass[$order['status']] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?php echo $class; ?>">
                                                <?php echo ucfirst($order['status']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo formatDate($order['created_at']); ?></td>
                                        <td>
                                            <a href="<?php echo SITE_URL; ?>/admin/order-details.php?id=<?php echo $order['id']; ?>" 
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Inquiries -->
        <div class="col-lg-4">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-comments me-2 text-primary"></i>
                        Recent Inquiries
                    </h6>
                    <a href="<?php echo SITE_URL; ?>/admin/inquiries.php" class="btn btn-outline-primary btn-sm">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_inquiries)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-inbox text-muted" style="font-size: 2rem; opacity: 0.3;"></i>
                            <p class="text-muted mt-2 mb-0">No recent inquiries</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($recent_inquiries as $inquiry): ?>
                            <div class="border-bottom pb-3 mb-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($inquiry['subject']); ?></h6>
                                        <p class="text-muted small mb-1">
                                            From: <?php echo htmlspecialchars($inquiry['name']); ?> 
                                            (<?php echo htmlspecialchars($inquiry['email']); ?>)
                                        </p>
                                        <small class="text-muted"><?php echo timeAgo($inquiry['created_at']); ?></small>
                                    </div>
                                    <span class="badge bg-<?php echo $inquiry['status'] === 'new' ? 'danger' : 'success'; ?> ms-2">
                                        <?php echo ucfirst($inquiry['status']); ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
