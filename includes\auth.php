<?php
/**
 * Authentication System
 * websitedeveloper0002.in
 */

class Auth {
    private $db;
    
    public function __construct() {
        global $db;
        $this->db = $db;
    }
    
    /**
     * Register new user
     */
    public function register($username, $email, $password, $fullName, $phone = null, $role = 'customer') {
        try {
            // Check if user already exists
            if ($this->userExists($username, $email)) {
                return ['success' => false, 'message' => __('user_already_exists')];
            }
            
            // Validate input
            if (!$this->validateRegistrationData($username, $email, $password, $fullName)) {
                return ['success' => false, 'message' => __('invalid_registration_data')];
            }
            
            // Hash password
            $hashedPassword = hashPassword($password);
            
            // Insert user
            $userData = [
                'username' => $username,
                'email' => $email,
                'password' => $hashedPassword,
                'full_name' => $fullName,
                'phone' => $phone,
                'role' => $role,
                'status' => 'active',
                'email_verified' => false
            ];
            
            $userId = $this->db->insert('users', $userData);
            
            if ($userId) {
                // Send welcome email
                $this->sendWelcomeEmail($email, $fullName);
                
                logActivity("New user registered: $username ($email)");
                return ['success' => true, 'message' => __('registration_successful'), 'user_id' => $userId];
            }
            
            return ['success' => false, 'message' => __('registration_failed')];
            
        } catch (Exception $e) {
            logActivity("Registration error: " . $e->getMessage(), 'error');
            return ['success' => false, 'message' => __('registration_error')];
        }
    }
    
    /**
     * Login user
     */
    public function login($username, $password, $rememberMe = false) {
        try {
            // Get user by username or email
            $user = $this->getUserByUsernameOrEmail($username);
            
            if (!$user) {
                return ['success' => false, 'message' => __('invalid_credentials')];
            }
            
            // Check if user is active
            if ($user['status'] !== 'active') {
                return ['success' => false, 'message' => __('account_suspended')];
            }
            
            // Verify password
            if (!verifyPassword($password, $user['password'])) {
                return ['success' => false, 'message' => __('invalid_credentials')];
            }
            
            // Create session
            $this->createUserSession($user, $rememberMe);
            
            logActivity("User logged in: {$user['username']} ({$user['email']})");
            return ['success' => true, 'message' => __('login_successful'), 'user' => $user];
            
        } catch (Exception $e) {
            logActivity("Login error: " . $e->getMessage(), 'error');
            return ['success' => false, 'message' => __('login_error')];
        }
    }
    
    /**
     * Logout user
     */
    public function logout() {
        try {
            // Remove session from database
            if (isset($_SESSION['session_token'])) {
                $this->db->delete('user_sessions', 'session_token = ?', [$_SESSION['session_token']]);
            }
            
            // Clear session
            session_unset();
            session_destroy();
            
            // Clear remember me cookie
            if (isset($_COOKIE['remember_token'])) {
                setcookie('remember_token', '', time() - 3600, '/');
            }
            
            return ['success' => true, 'message' => __('logout_successful')];
            
        } catch (Exception $e) {
            logActivity("Logout error: " . $e->getMessage(), 'error');
            return ['success' => false, 'message' => __('logout_error')];
        }
    }
    
    /**
     * Check if user is logged in
     */
    public function isLoggedIn() {
        return isset($_SESSION['user_id']) && isset($_SESSION['session_token']);
    }
    
    /**
     * Get current user
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        $user = $this->db->fetch(
            'SELECT * FROM users WHERE id = ? AND status = "active"',
            [$_SESSION['user_id']]
        );
        
        return $user ?: null;
    }
    
    /**
     * Check user role
     */
    public function hasRole($role) {
        $user = $this->getCurrentUser();
        return $user && $user['role'] === $role;
    }
    
    /**
     * Require login
     */
    public function requireLogin() {
        if (!$this->isLoggedIn()) {
            redirect('/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
        }
    }
    
    /**
     * Require specific role
     */
    public function requireRole($role) {
        $this->requireLogin();
        if (!$this->hasRole($role)) {
            redirect('/unauthorized.php');
        }
    }
    
    /**
     * Change password
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        try {
            $user = $this->db->fetch('SELECT password FROM users WHERE id = ?', [$userId]);
            
            if (!$user || !verifyPassword($currentPassword, $user['password'])) {
                return ['success' => false, 'message' => __('current_password_incorrect')];
            }
            
            $hashedPassword = hashPassword($newPassword);
            $this->db->update('users', ['password' => $hashedPassword], 'id = ?', [$userId]);
            
            logActivity("Password changed for user ID: $userId");
            return ['success' => true, 'message' => __('password_changed_successfully')];
            
        } catch (Exception $e) {
            logActivity("Password change error: " . $e->getMessage(), 'error');
            return ['success' => false, 'message' => __('password_change_error')];
        }
    }
    
    /**
     * Reset password
     */
    public function requestPasswordReset($email) {
        try {
            $user = $this->db->fetch('SELECT * FROM users WHERE email = ?', [$email]);
            
            if (!$user) {
                return ['success' => false, 'message' => __('email_not_found')];
            }
            
            // Generate reset token
            $token = generateRandomString(32);
            $expiresAt = date('Y-m-d H:i:s', strtotime('+1 hour'));
            
            // Save token
            $this->db->insert('password_reset_tokens', [
                'user_id' => $user['id'],
                'token' => $token,
                'expires_at' => $expiresAt
            ]);
            
            // Send reset email
            $this->sendPasswordResetEmail($user['email'], $user['full_name'], $token);
            
            return ['success' => true, 'message' => __('password_reset_email_sent')];
            
        } catch (Exception $e) {
            logActivity("Password reset request error: " . $e->getMessage(), 'error');
            return ['success' => false, 'message' => __('password_reset_error')];
        }
    }
    
    // Private helper methods
    
    private function userExists($username, $email) {
        $user = $this->db->fetch(
            'SELECT id FROM users WHERE username = ? OR email = ?',
            [$username, $email]
        );
        return $user !== false;
    }
    
    private function validateRegistrationData($username, $email, $password, $fullName) {
        return !empty($username) && 
               !empty($email) && 
               !empty($password) && 
               !empty($fullName) &&
               isValidEmail($email) &&
               strlen($password) >= 6 &&
               strlen($username) >= 3;
    }
    
    private function getUserByUsernameOrEmail($username) {
        return $this->db->fetch(
            'SELECT * FROM users WHERE username = ? OR email = ?',
            [$username, $username]
        );
    }
    
    private function createUserSession($user, $rememberMe = false) {
        // Generate session token
        $sessionToken = generateRandomString(32);
        $expiresAt = date('Y-m-d H:i:s', strtotime($rememberMe ? '+30 days' : '+24 hours'));
        
        // Save session to database
        $this->db->insert('user_sessions', [
            'user_id' => $user['id'],
            'session_token' => $sessionToken,
            'expires_at' => $expiresAt,
            'ip_address' => getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
        
        // Set session variables
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['session_token'] = $sessionToken;
        
        // Set remember me cookie
        if ($rememberMe) {
            setcookie('remember_token', $sessionToken, time() + (30 * 24 * 60 * 60), '/');
        }
    }
    
    private function sendWelcomeEmail($email, $name) {
        // Implement email sending logic
        return true;
    }
    
    private function sendPasswordResetEmail($email, $name, $token) {
        // Implement email sending logic
        return true;
    }
}

// Global auth instance
$auth = new Auth();

// Auto-login from remember me cookie
if (!$auth->isLoggedIn() && isset($_COOKIE['remember_token'])) {
    $session = $db->fetch(
        'SELECT u.* FROM users u 
         JOIN user_sessions s ON u.id = s.user_id 
         WHERE s.session_token = ? AND s.expires_at > NOW() AND u.status = "active"',
        [$_COOKIE['remember_token']]
    );
    
    if ($session) {
        $_SESSION['user_id'] = $session['id'];
        $_SESSION['username'] = $session['username'];
        $_SESSION['role'] = $session['role'];
        $_SESSION['session_token'] = $_COOKIE['remember_token'];
    }
}
?>
