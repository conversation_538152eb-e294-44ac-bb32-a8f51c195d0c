<?php
/**
 * Email System Functions
 * PHPMailer Integration
 */

require_once __DIR__ . '/../config/config.php';

// Include PHPMailer (you'll need to install via Composer: composer require phpmailer/phpmailer)
// For now, we'll create a basic email system that can be upgraded to PHPMailer later

/**
 * Send email using PHP mail() function
 * This is a basic implementation - upgrade to PHPMailer for production
 */
function sendEmail($to, $subject, $message, $from_name = null, $from_email = null) {
    try {
        $from_name = $from_name ?? SMTP_FROM_NAME;
        $from_email = $from_email ?? SMTP_FROM_EMAIL;
        
        // Headers
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: ' . $from_name . ' <' . $from_email . '>',
            'Reply-To: ' . $from_email,
            'X-Mailer: PHP/' . phpversion()
        ];
        
        $header_string = implode("\r\n", $headers);
        
        // Send email
        $result = mail($to, $subject, $message, $header_string);
        
        if ($result) {
            logActivity("Email sent successfully to: $to - Subject: $subject");
            return true;
        } else {
            logActivity("Failed to send email to: $to - Subject: $subject", 'error');
            return false;
        }
    } catch (Exception $e) {
        logActivity("Email sending error: " . $e->getMessage(), 'error');
        return false;
    }
}

/**
 * Send welcome email to new users
 */
function sendWelcomeEmail($user) {
    $subject = "Welcome to " . SITE_NAME . "!";
    
    $message = getEmailTemplate('welcome', [
        'user_name' => $user['full_name'],
        'username' => $user['username'],
        'site_name' => SITE_NAME,
        'site_url' => SITE_URL,
        'login_url' => SITE_URL . '/login.php'
    ]);
    
    return sendEmail($user['email'], $subject, $message);
}

/**
 * Send password reset email
 */
function sendPasswordResetEmail($user, $reset_token) {
    $subject = "Password Reset Request - " . SITE_NAME;
    
    $reset_url = SITE_URL . '/reset-password.php?token=' . $reset_token;
    
    $message = getEmailTemplate('password_reset', [
        'user_name' => $user['full_name'],
        'reset_url' => $reset_url,
        'site_name' => SITE_NAME,
        'expiry_time' => '24 hours'
    ]);
    
    return sendEmail($user['email'], $subject, $message);
}

/**
 * Send order confirmation email
 */
function sendOrderConfirmationEmail($order, $user) {
    $subject = "Order Confirmation - " . $order['order_number'];
    
    $message = getEmailTemplate('order_confirmation', [
        'user_name' => $user['full_name'],
        'order_number' => $order['order_number'],
        'package_name' => $order['name_en'],
        'total_amount' => formatCurrency($order['total_amount']),
        'order_date' => formatDate($order['created_at']),
        'site_name' => SITE_NAME,
        'dashboard_url' => SITE_URL . '/dashboard.php'
    ]);
    
    return sendEmail($user['email'], $subject, $message);
}

/**
 * Send inquiry confirmation email
 */
function sendInquiryConfirmationEmail($inquiry) {
    $subject = "Thank you for your inquiry - " . SITE_NAME;
    
    $message = getEmailTemplate('inquiry_confirmation', [
        'user_name' => $inquiry['name'],
        'inquiry_subject' => $inquiry['subject'],
        'site_name' => SITE_NAME,
        'contact_email' => SITE_EMAIL,
        'contact_phone' => SITE_PHONE
    ]);
    
    return sendEmail($inquiry['email'], $subject, $message);
}

/**
 * Send newsletter email
 */
function sendNewsletterEmail($subscriber, $newsletter_content) {
    $subject = $newsletter_content['subject'] ?? "Newsletter - " . SITE_NAME;
    
    $message = getEmailTemplate('newsletter', [
        'subscriber_name' => $subscriber['name'] ?? 'Valued Customer',
        'newsletter_content' => $newsletter_content['content'],
        'site_name' => SITE_NAME,
        'unsubscribe_url' => SITE_URL . '/unsubscribe.php?token=' . ($subscriber['unsubscribe_token'] ?? '')
    ]);
    
    return sendEmail($subscriber['email'], $subject, $message);
}

/**
 * Get email template
 */
function getEmailTemplate($template_name, $variables = []) {
    global $db;
    
    // Try to get template from database first
    $template = $db->fetch(
        'SELECT * FROM email_templates WHERE name = ? AND is_active = 1',
        [$template_name]
    );
    
    if ($template) {
        $content = $template['content'];
    } else {
        // Fallback to default templates
        $content = getDefaultEmailTemplate($template_name);
    }
    
    // Replace variables
    foreach ($variables as $key => $value) {
        $content = str_replace('{{' . $key . '}}', $value, $content);
    }
    
    // Wrap in email layout
    return getEmailLayout($content, $variables);
}

/**
 * Get default email templates
 */
function getDefaultEmailTemplate($template_name) {
    $templates = [
        'welcome' => '
            <h2>Welcome to {{site_name}}!</h2>
            <p>Dear {{user_name}},</p>
            <p>Thank you for registering with us! Your account has been successfully created.</p>
            <p><strong>Username:</strong> {{username}}</p>
            <p>You can now log in to your account and explore our services.</p>
            <p><a href="{{login_url}}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Login to Your Account</a></p>
            <p>If you have any questions, feel free to contact us.</p>
        ',
        
        'password_reset' => '
            <h2>Password Reset Request</h2>
            <p>Dear {{user_name}},</p>
            <p>We received a request to reset your password. Click the link below to reset your password:</p>
            <p><a href="{{reset_url}}" style="background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
            <p>This link will expire in {{expiry_time}}.</p>
            <p>If you did not request this password reset, please ignore this email.</p>
        ',
        
        'order_confirmation' => '
            <h2>Order Confirmation</h2>
            <p>Dear {{user_name}},</p>
            <p>Thank you for your order! We have received your order and payment.</p>
            <h3>Order Details:</h3>
            <ul>
                <li><strong>Order Number:</strong> {{order_number}}</li>
                <li><strong>Package:</strong> {{package_name}}</li>
                <li><strong>Total Amount:</strong> {{total_amount}}</li>
                <li><strong>Order Date:</strong> {{order_date}}</li>
            </ul>
            <p>We will start working on your project soon and keep you updated on the progress.</p>
            <p><a href="{{dashboard_url}}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Order Status</a></p>
        ',
        
        'inquiry_confirmation' => '
            <h2>Thank You for Your Inquiry</h2>
            <p>Dear {{user_name}},</p>
            <p>Thank you for contacting us! We have received your inquiry about "{{inquiry_subject}}".</p>
            <p>Our team will review your message and get back to you within 24 hours.</p>
            <p>If you need immediate assistance, you can reach us at:</p>
            <ul>
                <li><strong>Email:</strong> {{contact_email}}</li>
                <li><strong>Phone:</strong> {{contact_phone}}</li>
            </ul>
        ',
        
        'newsletter' => '
            <h2>{{site_name}} Newsletter</h2>
            <p>Dear {{subscriber_name}},</p>
            {{newsletter_content}}
            <hr>
            <p><small>You are receiving this email because you subscribed to our newsletter. 
            <a href="{{unsubscribe_url}}">Unsubscribe</a> if you no longer wish to receive these emails.</small></p>
        '
    ];
    
    return $templates[$template_name] ?? '<p>Template not found.</p>';
}

/**
 * Get email layout wrapper
 */
function getEmailLayout($content, $variables = []) {
    $site_name = $variables['site_name'] ?? SITE_NAME;
    $site_url = $variables['site_url'] ?? SITE_URL;
    
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>$site_name</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #007bff; color: white; padding: 20px; text-align: center; }
            .content { padding: 30px 20px; background-color: #ffffff; }
            .footer { background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666; }
            a { color: #007bff; text-decoration: none; }
            a:hover { text-decoration: underline; }
            .btn { display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>$site_name</h1>
            </div>
            <div class='content'>
                $content
            </div>
            <div class='footer'>
                <p>&copy; " . date('Y') . " $site_name. All rights reserved.</p>
                <p>Visit us at: <a href='$site_url'>$site_url</a></p>
            </div>
        </div>
    </body>
    </html>
    ";
}

/**
 * Send bulk emails (for newsletters)
 */
function sendBulkEmails($recipients, $subject, $content, $batch_size = 50) {
    $sent_count = 0;
    $failed_count = 0;
    
    // Process in batches to avoid overwhelming the server
    $batches = array_chunk($recipients, $batch_size);
    
    foreach ($batches as $batch) {
        foreach ($batch as $recipient) {
            $personalized_content = str_replace(
                ['{{name}}', '{{email}}'],
                [$recipient['name'] ?? 'Valued Customer', $recipient['email']],
                $content
            );
            
            if (sendEmail($recipient['email'], $subject, $personalized_content)) {
                $sent_count++;
            } else {
                $failed_count++;
            }
            
            // Small delay to prevent overwhelming the mail server
            usleep(100000); // 0.1 second delay
        }
        
        // Longer delay between batches
        sleep(1);
    }
    
    logActivity("Bulk email sent: $sent_count successful, $failed_count failed");
    
    return [
        'sent' => $sent_count,
        'failed' => $failed_count,
        'total' => count($recipients)
    ];
}

/**
 * Queue email for later sending
 */
function queueEmail($to, $subject, $message, $send_at = null) {
    global $db;
    
    $send_at = $send_at ?? date('Y-m-d H:i:s');
    
    $data = [
        'to_email' => $to,
        'subject' => $subject,
        'message' => $message,
        'status' => 'pending',
        'send_at' => $send_at,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    return $db->insert('email_queue', $data);
}

/**
 * Process email queue
 */
function processEmailQueue($limit = 10) {
    global $db;
    
    $emails = $db->fetchAll(
        'SELECT * FROM email_queue 
         WHERE status = "pending" AND send_at <= NOW() 
         ORDER BY created_at ASC 
         LIMIT ?',
        [$limit]
    );
    
    $processed = 0;
    
    foreach ($emails as $email) {
        if (sendEmail($email['to_email'], $email['subject'], $email['message'])) {
            $db->update('email_queue', ['status' => 'sent', 'sent_at' => date('Y-m-d H:i:s')], 'id = ?', [$email['id']]);
        } else {
            $db->update('email_queue', ['status' => 'failed', 'attempts' => $email['attempts'] + 1], 'id = ?', [$email['id']]);
        }
        $processed++;
    }
    
    return $processed;
}

/**
 * Validate email address
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Add email subscriber
 */
function addEmailSubscriber($email, $name = '', $source = 'website') {
    global $db;
    
    if (!isValidEmail($email)) {
        return false;
    }
    
    // Check if already subscribed
    $existing = $db->fetch('SELECT id FROM email_subscribers WHERE email = ?', [$email]);
    
    if ($existing) {
        return false; // Already subscribed
    }
    
    $data = [
        'email' => $email,
        'name' => $name,
        'source' => $source,
        'status' => 'active',
        'subscribed_at' => date('Y-m-d H:i:s'),
        'unsubscribe_token' => bin2hex(random_bytes(32))
    ];
    
    return $db->insert('email_subscribers', $data);
}

/**
 * Unsubscribe email
 */
function unsubscribeEmail($token) {
    global $db;
    
    $subscriber = $db->fetch('SELECT * FROM email_subscribers WHERE unsubscribe_token = ?', [$token]);
    
    if ($subscriber) {
        $db->update('email_subscribers', ['status' => 'unsubscribed', 'unsubscribed_at' => date('Y-m-d H:i:s')], 'id = ?', [$subscriber['id']]);
        return true;
    }
    
    return false;
}
?>
