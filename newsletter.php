<?php
$page_title = __('newsletter');
require_once 'includes/header.php';

$error = '';
$success = '';

// Handle newsletter subscription
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'subscribe') {
        $email = sanitize($_POST['email'] ?? '');
        $name = sanitize($_POST['name'] ?? '');
        
        if (empty($email)) {
            $error = __('email_required');
        } elseif (!isValidEmail($email)) {
            $error = __('invalid_email');
        } else {
            $result = addEmailSubscriber($email, $name, 'newsletter_page');
            
            if ($result) {
                $success = __('newsletter_subscribed');
                
                // Send welcome email
                sendEmail($email, "Welcome to " . SITE_NAME . " Newsletter!", 
                    getEmailTemplate('newsletter_welcome', [
                        'subscriber_name' => $name ?: 'Valued Subscriber',
                        'site_name' => SITE_NAME,
                        'site_url' => SITE_URL
                    ])
                );
                
                logActivity("Newsletter subscription: $email");
            } else {
                $error = 'You are already subscribed to our newsletter or an error occurred.';
            }
        }
    }
}

// Get recent newsletters (if admin wants to show them)
$recent_newsletters = [];
if (isset($db)) {
    $recent_newsletters = $db->fetchAll(
        'SELECT * FROM email_templates 
         WHERE name LIKE "newsletter_%" AND is_active = 1 
         ORDER BY created_at DESC 
         LIMIT 5'
    );
}
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Newsletter Header -->
            <div class="text-center mb-5">
                <h1 class="display-4 mb-3"><?php echo __('newsletter_title'); ?></h1>
                <p class="lead text-muted">
                    Stay updated with the latest web development trends, tips, and exclusive offers from <?php echo SITE_NAME; ?>
                </p>
            </div>
            
            <!-- Subscription Form -->
            <div class="card shadow-custom border-radius-custom mb-5">
                <div class="card-body p-4">
                    <h3 class="card-title text-center mb-4">
                        <i class="fas fa-envelope me-2 text-primary"></i>
                        Subscribe to Our Newsletter
                    </h3>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="">
                        <input type="hidden" name="action" value="subscribe">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label"><?php echo __('full_name'); ?> (Optional)</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       placeholder="Your name">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label"><?php echo __('email'); ?> *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       placeholder="<EMAIL>" required>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i>
                                Subscribe Now
                            </button>
                        </div>
                        
                        <p class="text-muted text-center mt-3 mb-0">
                            <small>
                                <i class="fas fa-shield-alt me-1"></i>
                                We respect your privacy. Unsubscribe at any time.
                            </small>
                        </p>
                    </form>
                </div>
            </div>
            
            <!-- Newsletter Benefits -->
            <div class="row mb-5">
                <div class="col-md-4 mb-4">
                    <div class="card h-100 border-0 text-center">
                        <div class="card-body">
                            <div class="mb-3">
                                <i class="fas fa-lightbulb text-warning" style="font-size: 3rem;"></i>
                            </div>
                            <h5 class="card-title">Web Development Tips</h5>
                            <p class="card-text text-muted">
                                Get expert tips and best practices for modern web development
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card h-100 border-0 text-center">
                        <div class="card-body">
                            <div class="mb-3">
                                <i class="fas fa-tags text-success" style="font-size: 3rem;"></i>
                            </div>
                            <h5 class="card-title">Exclusive Offers</h5>
                            <p class="card-text text-muted">
                                Be the first to know about special discounts and promotions
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card h-100 border-0 text-center">
                        <div class="card-body">
                            <div class="mb-3">
                                <i class="fas fa-chart-line text-info" style="font-size: 3rem;"></i>
                            </div>
                            <h5 class="card-title">Industry Updates</h5>
                            <p class="card-text text-muted">
                                Stay informed about the latest trends in web technology
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- What You'll Get -->
            <div class="card shadow-custom border-radius-custom mb-5">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-gift me-2 text-primary"></i>
                        What You'll Get
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Weekly web development tutorials
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Case studies from real projects
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Free resources and templates
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Early access to new services
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Industry news and updates
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Exclusive discount codes
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Expert insights and tips
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    No spam, unsubscribe anytime
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Newsletters (if any) -->
            <?php if (!empty($recent_newsletters)): ?>
            <div class="card shadow-custom border-radius-custom">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-newspaper me-2 text-primary"></i>
                        Recent Newsletters
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($recent_newsletters as $newsletter): ?>
                        <div class="col-md-6 mb-3">
                            <div class="card border">
                                <div class="card-body">
                                    <h6 class="card-title"><?php echo htmlspecialchars($newsletter['subject']); ?></h6>
                                    <p class="card-text text-muted small">
                                        <?php echo formatDate($newsletter['created_at']); ?>
                                    </p>
                                    <a href="#" class="btn btn-outline-primary btn-sm">Read More</a>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- FAQ Section -->
            <div class="mt-5">
                <h3 class="text-center mb-4">Frequently Asked Questions</h3>
                
                <div class="accordion" id="newsletterFAQ">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq1">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#collapse1">
                                How often will I receive newsletters?
                            </button>
                        </h2>
                        <div id="collapse1" class="accordion-collapse collapse" data-bs-parent="#newsletterFAQ">
                            <div class="accordion-body">
                                We send newsletters weekly, typically on Fridays. You'll receive valuable content 
                                without overwhelming your inbox.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq2">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#collapse2">
                                Can I unsubscribe at any time?
                            </button>
                        </h2>
                        <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#newsletterFAQ">
                            <div class="accordion-body">
                                Absolutely! Every email includes an unsubscribe link. You can opt out at any time 
                                with just one click.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq3">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#collapse3">
                                Will you share my email address?
                            </button>
                        </h2>
                        <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#newsletterFAQ">
                            <div class="accordion-body">
                                Never! We respect your privacy and will never share, sell, or rent your email address 
                                to third parties.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq4">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#collapse4">
                                What type of content will I receive?
                            </button>
                        </h2>
                        <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#newsletterFAQ">
                            <div class="accordion-body">
                                You'll receive web development tutorials, industry news, case studies, free resources, 
                                and exclusive offers on our services.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
