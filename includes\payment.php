<?php
/**
 * Payment Processing Functions
 * Razorpay Integration
 */

require_once __DIR__ . '/../config/config.php';

/**
 * Create Razorpay order
 */
function createRazorpayOrder($order) {
    try {
        $api_key = RAZORPAY_KEY_ID;
        $api_secret = RAZORPAY_KEY_SECRET;
        
        $amount = ($order['total_amount'] + ($order['tax_amount'] ?? 0)) * 100; // Convert to paise
        
        $order_data = [
            'receipt' => $order['order_number'],
            'amount' => $amount,
            'currency' => 'INR',
            'notes' => [
                'order_id' => $order['id'],
                'user_id' => $order['user_id'],
                'package_type' => $order['package_type'] ?? 'custom'
            ]
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.razorpay.com/v1/orders');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($order_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Basic ' . base64_encode($api_key . ':' . $api_secret)
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code === 200) {
            $response_data = json_decode($response, true);
            return $response_data['id'] ?? null;
        } else {
            logActivity("Razorpay order creation failed: HTTP $http_code - $response", 'error');
            return false;
        }
    } catch (Exception $e) {
        logActivity("Razorpay order creation error: " . $e->getMessage(), 'error');
        return false;
    }
}

/**
 * Verify Razorpay payment signature
 */
function verifyRazorpayPayment($payment_id, $order_id, $signature) {
    try {
        $api_secret = RAZORPAY_KEY_SECRET;
        
        $generated_signature = hash_hmac('sha256', $order_id . '|' . $payment_id, $api_secret);
        
        if (hash_equals($generated_signature, $signature)) {
            // Additional verification by fetching payment details
            $payment_details = getRazorpayPaymentDetails($payment_id);
            
            if ($payment_details && $payment_details['status'] === 'captured') {
                return [
                    'success' => true,
                    'message' => 'Payment verified successfully',
                    'payment_details' => $payment_details
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Payment not captured or invalid'
                ];
            }
        } else {
            return [
                'success' => false,
                'message' => 'Invalid payment signature'
            ];
        }
    } catch (Exception $e) {
        logActivity("Payment verification error: " . $e->getMessage(), 'error');
        return [
            'success' => false,
            'message' => 'Payment verification failed'
        ];
    }
}

/**
 * Get Razorpay payment details
 */
function getRazorpayPaymentDetails($payment_id) {
    try {
        $api_key = RAZORPAY_KEY_ID;
        $api_secret = RAZORPAY_KEY_SECRET;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.razorpay.com/v1/payments/$payment_id");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Basic ' . base64_encode($api_key . ':' . $api_secret)
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code === 200) {
            return json_decode($response, true);
        } else {
            logActivity("Failed to fetch payment details: HTTP $http_code - $response", 'error');
            return false;
        }
    } catch (Exception $e) {
        logActivity("Error fetching payment details: " . $e->getMessage(), 'error');
        return false;
    }
}

/**
 * Process Razorpay webhook
 */
function processRazorpayWebhook($payload, $signature) {
    try {
        $webhook_secret = RAZORPAY_WEBHOOK_SECRET;
        
        // Verify webhook signature
        $generated_signature = hash_hmac('sha256', $payload, $webhook_secret);
        
        if (!hash_equals($generated_signature, $signature)) {
            logActivity("Invalid webhook signature", 'error');
            return false;
        }
        
        $data = json_decode($payload, true);
        $event = $data['event'] ?? '';
        $payment_entity = $data['payload']['payment']['entity'] ?? null;
        
        if (!$payment_entity) {
            logActivity("Invalid webhook payload", 'error');
            return false;
        }
        
        global $db;
        
        switch ($event) {
            case 'payment.captured':
                // Update order status to paid
                $order = $db->fetch(
                    'SELECT * FROM orders WHERE razorpay_order_id = ?',
                    [$payment_entity['order_id']]
                );
                
                if ($order) {
                    $updateData = [
                        'payment_status' => 'paid',
                        'status' => 'paid',
                        'razorpay_payment_id' => $payment_entity['id'],
                        'paid_at' => date('Y-m-d H:i:s')
                    ];
                    
                    $db->update('orders', $updateData, 'id = ?', [$order['id']]);
                    logActivity("Payment captured via webhook: Order {$order['order_number']}");
                }
                break;
                
            case 'payment.failed':
                // Update order status to failed
                $order = $db->fetch(
                    'SELECT * FROM orders WHERE razorpay_order_id = ?',
                    [$payment_entity['order_id']]
                );
                
                if ($order) {
                    $db->update('orders', ['payment_status' => 'failed'], 'id = ?', [$order['id']]);
                    logActivity("Payment failed via webhook: Order {$order['order_number']}");
                }
                break;
        }
        
        return true;
    } catch (Exception $e) {
        logActivity("Webhook processing error: " . $e->getMessage(), 'error');
        return false;
    }
}

/**
 * Initiate refund
 */
function initiateRefund($payment_id, $amount, $reason = '') {
    try {
        $api_key = RAZORPAY_KEY_ID;
        $api_secret = RAZORPAY_KEY_SECRET;
        
        $refund_data = [
            'amount' => $amount * 100, // Convert to paise
            'speed' => 'normal'
        ];
        
        if ($reason) {
            $refund_data['notes'] = ['reason' => $reason];
        }
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.razorpay.com/v1/payments/$payment_id/refund");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($refund_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Basic ' . base64_encode($api_key . ':' . $api_secret)
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code === 200) {
            $response_data = json_decode($response, true);
            logActivity("Refund initiated: Payment ID $payment_id, Amount: $amount, Refund ID: {$response_data['id']}");
            return $response_data;
        } else {
            logActivity("Refund initiation failed: HTTP $http_code - $response", 'error');
            return false;
        }
    } catch (Exception $e) {
        logActivity("Refund initiation error: " . $e->getMessage(), 'error');
        return false;
    }
}

/**
 * Get refund status
 */
function getRefundStatus($refund_id) {
    try {
        $api_key = RAZORPAY_KEY_ID;
        $api_secret = RAZORPAY_KEY_SECRET;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.razorpay.com/v1/refunds/$refund_id");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Basic ' . base64_encode($api_key . ':' . $api_secret)
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code === 200) {
            return json_decode($response, true);
        } else {
            logActivity("Failed to fetch refund status: HTTP $http_code - $response", 'error');
            return false;
        }
    } catch (Exception $e) {
        logActivity("Error fetching refund status: " . $e->getMessage(), 'error');
        return false;
    }
}

/**
 * Send order confirmation email
 */
function sendOrderConfirmationEmail($order, $user) {
    try {
        $subject = "Order Confirmation - " . $order['order_number'];
        
        $message = "
        <h2>Order Confirmation</h2>
        <p>Dear {$user['full_name']},</p>
        <p>Thank you for your order! Your payment has been successfully processed.</p>
        
        <h3>Order Details:</h3>
        <ul>
            <li><strong>Order Number:</strong> {$order['order_number']}</li>
            <li><strong>Package:</strong> {$order['name_en']}</li>
            <li><strong>Amount:</strong> " . formatCurrency($order['total_amount']) . "</li>
            <li><strong>Payment Status:</strong> Paid</li>
        </ul>
        
        <p>We will start working on your project soon and will keep you updated on the progress.</p>
        <p>If you have any questions, please don't hesitate to contact us.</p>
        
        <p>Best regards,<br>
        " . SITE_NAME . " Team</p>
        ";
        
        return sendEmail($user['email'], $subject, $message);
    } catch (Exception $e) {
        logActivity("Error sending order confirmation email: " . $e->getMessage(), 'error');
        return false;
    }
}
?>
