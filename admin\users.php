<?php
$page_title = __('manage_users');
require_once '../includes/header.php';

// Require admin role
$auth->requireRole('admin');

$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $user_id = (int)($_POST['user_id'] ?? 0);
    
    if ($action === 'update_status' && $user_id) {
        $status = sanitize($_POST['status'] ?? '');
        
        if (in_array($status, ['active', 'inactive', 'suspended'])) {
            try {
                $result = $db->update('users', ['status' => $status], 'id = ? AND role != "admin"', [$user_id]);
                
                if ($result) {
                    $success = 'User status updated successfully';
                    logActivity("User status updated: ID $user_id to $status");
                } else {
                    $error = 'Failed to update user status';
                }
            } catch (Exception $e) {
                logActivity("User status update error: " . $e->getMessage(), 'error');
                $error = 'An error occurred while updating user status';
            }
        }
    } elseif ($action === 'update_role' && $user_id) {
        $role = sanitize($_POST['role'] ?? '');
        
        if (in_array($role, ['customer', 'vendor'])) {
            try {
                $result = $db->update('users', ['role' => $role], 'id = ? AND role != "admin"', [$user_id]);
                
                if ($result) {
                    $success = 'User role updated successfully';
                    logActivity("User role updated: ID $user_id to $role");
                } else {
                    $error = 'Failed to update user role';
                }
            } catch (Exception $e) {
                logActivity("User role update error: " . $e->getMessage(), 'error');
                $error = 'An error occurred while updating user role';
            }
        }
    }
}

// Pagination and filtering
$page = (int)($_GET['page'] ?? 1);
$limit = ITEMS_PER_PAGE;
$offset = ($page - 1) * $limit;
$search = sanitize($_GET['search'] ?? '');
$status_filter = sanitize($_GET['status'] ?? '');
$role_filter = sanitize($_GET['role'] ?? '');

// Build query
$where_conditions = ['role != "admin"'];
$params = [];

if ($search) {
    $where_conditions[] = '(username LIKE ? OR email LIKE ? OR full_name LIKE ?)';
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param]);
}

if ($status_filter) {
    $where_conditions[] = 'status = ?';
    $params[] = $status_filter;
}

if ($role_filter) {
    $where_conditions[] = 'role = ?';
    $params[] = $role_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count
$total_users = $db->fetch(
    "SELECT COUNT(*) as count FROM users WHERE $where_clause",
    $params
)['count'];

$total_pages = ceil($total_users / $limit);

// Get users
$users = $db->fetchAll(
    "SELECT u.*, 
            (SELECT COUNT(*) FROM orders WHERE user_id = u.id) as order_count,
            (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE user_id = u.id AND payment_status = 'paid') as total_spent
     FROM users u 
     WHERE $where_clause 
     ORDER BY u.created_at DESC 
     LIMIT ? OFFSET ?",
    array_merge($params, [$limit, $offset])
);

// Get statistics
$stats = [
    'total_users' => $db->fetch('SELECT COUNT(*) as count FROM users WHERE role != "admin"')['count'],
    'active_users' => $db->fetch('SELECT COUNT(*) as count FROM users WHERE role != "admin" AND status = "active"')['count'],
    'customers' => $db->fetch('SELECT COUNT(*) as count FROM users WHERE role = "customer"')['count'],
    'vendors' => $db->fetch('SELECT COUNT(*) as count FROM users WHERE role = "vendor"')['count']
];
?>

<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1"><?php echo __('manage_users'); ?></h2>
                    <p class="text-muted mb-0">Manage user accounts and permissions</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-body text-center">
                    <h3 class="text-primary mb-1"><?php echo $stats['total_users']; ?></h3>
                    <p class="text-muted mb-0">Total Users</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-body text-center">
                    <h3 class="text-success mb-1"><?php echo $stats['active_users']; ?></h3>
                    <p class="text-muted mb-0">Active Users</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-body text-center">
                    <h3 class="text-info mb-1"><?php echo $stats['customers']; ?></h3>
                    <p class="text-muted mb-0">Customers</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-body text-center">
                    <h3 class="text-warning mb-1"><?php echo $stats['vendors']; ?></h3>
                    <p class="text-muted mb-0">Vendors</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filters and Search -->
    <div class="card shadow-custom border-radius-custom mb-4">
        <div class="card-body">
            <form method="GET" action="">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="search" class="form-label">Search Users</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Username, email, or name...">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-control" id="status" name="status">
                            <option value="">All Statuses</option>
                            <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                            <option value="suspended" <?php echo $status_filter === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-control" id="role" name="role">
                            <option value="">All Roles</option>
                            <option value="customer" <?php echo $role_filter === 'customer' ? 'selected' : ''; ?>>Customer</option>
                            <option value="vendor" <?php echo $role_filter === 'vendor' ? 'selected' : ''; ?>>Vendor</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Filter
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Users Table -->
    <div class="card shadow-custom border-radius-custom">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-users me-2 text-primary"></i>
                Users (<?php echo $total_users; ?> total)
            </h5>
        </div>
        <div class="card-body">
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <?php if (empty($users)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-users text-muted" style="font-size: 3rem; opacity: 0.3;"></i>
                    <h6 class="text-muted mt-3">No users found</h6>
                    <p class="text-muted">Try adjusting your search criteria.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Contact</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Orders</th>
                                <th>Total Spent</th>
                                <th>Joined</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($user['full_name']); ?></strong><br>
                                            <small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <i class="fas fa-envelope me-1"></i>
                                            <a href="mailto:<?php echo htmlspecialchars($user['email']); ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($user['email']); ?>
                                            </a>
                                        </div>
                                        <?php if ($user['phone']): ?>
                                            <div class="mt-1">
                                                <i class="fas fa-phone me-1"></i>
                                                <a href="tel:<?php echo htmlspecialchars($user['phone']); ?>" class="text-decoration-none">
                                                    <?php echo htmlspecialchars($user['phone']); ?>
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $user['role'] === 'vendor' ? 'warning' : 'info'; ?>">
                                            <?php echo ucfirst($user['role']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : ($user['status'] === 'suspended' ? 'danger' : 'secondary'); ?>">
                                            <?php echo ucfirst($user['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo $user['order_count']; ?></span>
                                    </td>
                                    <td>
                                        <?php echo formatCurrency($user['total_spent']); ?>
                                    </td>
                                    <td>
                                        <div><?php echo formatDate($user['created_at']); ?></div>
                                        <small class="text-muted"><?php echo timeAgo($user['created_at']); ?></small>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" 
                                                    data-bs-toggle="dropdown">
                                                Actions
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><h6 class="dropdown-header">Status</h6></li>
                                                <li>
                                                    <button class="dropdown-item" onclick="updateUserStatus(<?php echo $user['id']; ?>, 'active')">
                                                        <i class="fas fa-check text-success me-2"></i>Set Active
                                                    </button>
                                                </li>
                                                <li>
                                                    <button class="dropdown-item" onclick="updateUserStatus(<?php echo $user['id']; ?>, 'inactive')">
                                                        <i class="fas fa-pause text-warning me-2"></i>Set Inactive
                                                    </button>
                                                </li>
                                                <li>
                                                    <button class="dropdown-item" onclick="updateUserStatus(<?php echo $user['id']; ?>, 'suspended')">
                                                        <i class="fas fa-ban text-danger me-2"></i>Suspend
                                                    </button>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><h6 class="dropdown-header">Role</h6></li>
                                                <li>
                                                    <button class="dropdown-item" onclick="updateUserRole(<?php echo $user['id']; ?>, 'customer')">
                                                        <i class="fas fa-user text-info me-2"></i>Set Customer
                                                    </button>
                                                </li>
                                                <li>
                                                    <button class="dropdown-item" onclick="updateUserRole(<?php echo $user['id']; ?>, 'vendor')">
                                                        <i class="fas fa-store text-warning me-2"></i>Set Vendor
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="mt-4">
                        <?php 
                        $base_url = '/admin/users.php';
                        $query_params = [];
                        if ($search) $query_params['search'] = $search;
                        if ($status_filter) $query_params['status'] = $status_filter;
                        if ($role_filter) $query_params['role'] = $role_filter;
                        
                        if (!empty($query_params)) {
                            $base_url .= '?' . http_build_query($query_params) . '&';
                        } else {
                            $base_url .= '?';
                        }
                        
                        echo generatePagination($page, $total_pages, $base_url);
                        ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Hidden forms for actions -->
<form id="statusForm" method="POST" action="" style="display: none;">
    <input type="hidden" name="action" value="update_status">
    <input type="hidden" name="user_id" id="statusUserId">
    <input type="hidden" name="status" id="statusValue">
</form>

<form id="roleForm" method="POST" action="" style="display: none;">
    <input type="hidden" name="action" value="update_role">
    <input type="hidden" name="user_id" id="roleUserId">
    <input type="hidden" name="role" id="roleValue">
</form>

<script>
function updateUserStatus(userId, status) {
    if (confirm(`Are you sure you want to set this user's status to "${status}"?`)) {
        document.getElementById('statusUserId').value = userId;
        document.getElementById('statusValue').value = status;
        document.getElementById('statusForm').submit();
    }
}

function updateUserRole(userId, role) {
    if (confirm(`Are you sure you want to change this user's role to "${role}"?`)) {
        document.getElementById('roleUserId').value = userId;
        document.getElementById('roleValue').value = role;
        document.getElementById('roleForm').submit();
    }
}
</script>

<?php require_once '../includes/footer.php'; ?>
