<?php
$page_title = __('contact_us');
$page_description = 'Contact WebsiteDeveloper0002.in for professional web development services. Get in touch for business websites, e-commerce solutions, and custom development.';
require_once 'includes/header.php';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $phone = sanitize($_POST['phone'] ?? '');
    $subject = sanitize($_POST['subject'] ?? '');
    $message = sanitize($_POST['message'] ?? '');
    $package_interest = sanitize($_POST['package_interest'] ?? '');
    
    // Validation
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        $error = __('field_required');
    } elseif (!isValidEmail($email)) {
        $error = __('invalid_email');
    } elseif ($phone && !isValidPhone($phone)) {
        $error = __('invalid_phone');
    } else {
        // reCAPTCHA validation
        if (defined('RECAPTCHA_SECRET_KEY') && RECAPTCHA_SECRET_KEY) {
            $recaptcha_response = $_POST['g-recaptcha-response'] ?? '';
            if (empty($recaptcha_response)) {
                $error = 'Please complete the reCAPTCHA verification';
            } else {
                $verify_url = 'https://www.google.com/recaptcha/api/siteverify';
                $verify_data = [
                    'secret' => RECAPTCHA_SECRET_KEY,
                    'response' => $recaptcha_response,
                    'remoteip' => getClientIP()
                ];
                
                $verify_response = file_get_contents($verify_url . '?' . http_build_query($verify_data));
                $verify_result = json_decode($verify_response, true);
                
                if (!$verify_result['success']) {
                    $error = 'reCAPTCHA verification failed';
                }
            }
        }
        
        if (!$error) {
            try {
                // Save inquiry to database
                $inquiryData = [
                    'name' => $name,
                    'email' => $email,
                    'phone' => $phone,
                    'subject' => $subject,
                    'message' => $message,
                    'package_interest' => $package_interest ?: null,
                    'status' => 'new'
                ];
                
                $inquiryId = $db->insert('inquiries', $inquiryData);
                
                if ($inquiryId) {
                    // Send email notification (implement later)
                    // sendEmail($email, 'Thank you for contacting us', 'We will get back to you soon.');
                    
                    logActivity("New inquiry received from: $name ($email)");
                    $success = __('message_sent_successfully');
                    
                    // Clear form data
                    $name = $email = $phone = $subject = $message = $package_interest = '';
                } else {
                    $error = __('message_send_error');
                }
            } catch (Exception $e) {
                logActivity("Contact form error: " . $e->getMessage(), 'error');
                $error = __('message_send_error');
            }
        }
    }
}

// Get package from URL parameter
$selected_package = $_GET['package'] ?? '';
?>

<div class="container py-5">
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-envelope me-2 text-primary"></i>
                        <?php echo __('contact_form'); ?>
                    </h3>
                </div>
                <div class="card-body p-4">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label"><?php echo __('your_name'); ?> *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label"><?php echo __('your_email'); ?> *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label"><?php echo __('your_phone'); ?></label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($phone ?? ''); ?>" 
                                       placeholder="+91-9999999999">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="package_interest" class="form-label">Package Interest</label>
                                <select class="form-control" id="package_interest" name="package_interest">
                                    <option value="">Select a package (optional)</option>
                                    <option value="business" <?php echo $selected_package === 'business' ? 'selected' : ''; ?>>
                                        <?php echo __('business_package'); ?>
                                    </option>
                                    <option value="ecommerce" <?php echo $selected_package === 'ecommerce' ? 'selected' : ''; ?>>
                                        <?php echo __('ecommerce_package'); ?>
                                    </option>
                                    <option value="custom" <?php echo $selected_package === 'custom' ? 'selected' : ''; ?>>
                                        <?php echo __('custom_package'); ?>
                                    </option>
                                    <option value="general">General Inquiry</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="subject" class="form-label"><?php echo __('subject'); ?> *</label>
                            <input type="text" class="form-control" id="subject" name="subject" 
                                   value="<?php echo htmlspecialchars($subject ?? ''); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label"><?php echo __('message'); ?> *</label>
                            <textarea class="form-control" id="message" name="message" rows="5" required><?php echo htmlspecialchars($message ?? ''); ?></textarea>
                        </div>
                        
                        <?php if (defined('RECAPTCHA_SITE_KEY') && RECAPTCHA_SITE_KEY): ?>
                        <div class="mb-3">
                            <div class="g-recaptcha" data-sitekey="<?php echo RECAPTCHA_SITE_KEY; ?>"></div>
                        </div>
                        <?php endif; ?>
                        
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane me-2"></i><?php echo __('send_message'); ?>
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow-custom border-radius-custom mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2 text-primary"></i>
                        <?php echo __('contact_info'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="contact-item mb-3">
                        <i class="fas fa-envelope text-primary me-3"></i>
                        <div>
                            <strong><?php echo __('email_us'); ?></strong><br>
                            <a href="mailto:<?php echo SITE_EMAIL; ?>" class="text-decoration-none">
                                <?php echo SITE_EMAIL; ?>
                            </a>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-3">
                        <i class="fas fa-phone text-primary me-3"></i>
                        <div>
                            <strong><?php echo __('call_us'); ?></strong><br>
                            <a href="tel:<?php echo SITE_PHONE; ?>" class="text-decoration-none">
                                <?php echo SITE_PHONE; ?>
                            </a>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-3">
                        <i class="fab fa-whatsapp text-primary me-3"></i>
                        <div>
                            <strong><?php echo __('whatsapp_us'); ?></strong><br>
                            <a href="https://wa.me/<?php echo str_replace(['+', '-', ' '], '', WHATSAPP_NUMBER); ?>" 
                               class="text-decoration-none" target="_blank">
                                <?php echo WHATSAPP_NUMBER; ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card shadow-custom border-radius-custom">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2 text-primary"></i>
                        Business Hours
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Monday - Friday</span>
                        <span>9:00 AM - 6:00 PM</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Saturday</span>
                        <span>10:00 AM - 4:00 PM</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Sunday</span>
                        <span class="text-muted">Closed</span>
                    </div>
                    <hr>
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        We respond to all inquiries within 24 hours
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.contact-item {
    display: flex;
    align-items: flex-start;
}

.contact-item i {
    margin-top: 2px;
}
</style>

<?php require_once 'includes/footer.php'; ?>
