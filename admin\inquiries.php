<?php
$page_title = __('manage_inquiries');
require_once '../includes/header.php';

// Require admin role
$auth->requireRole('admin');

$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $inquiry_id = (int)($_POST['inquiry_id'] ?? 0);
    
    if ($action === 'update_status' && $inquiry_id) {
        $status = sanitize($_POST['status'] ?? '');
        $admin_notes = sanitize($_POST['admin_notes'] ?? '');
        
        if (in_array($status, ['new', 'read', 'replied', 'closed'])) {
            try {
                $updateData = ['status' => $status];
                if ($admin_notes) {
                    $updateData['admin_notes'] = $admin_notes;
                }
                
                $result = $db->update('inquiries', $updateData, 'id = ?', [$inquiry_id]);
                
                if ($result) {
                    $success = 'Inquiry status updated successfully';
                    logActivity("Inquiry status updated: ID $inquiry_id to $status");
                } else {
                    $error = 'Failed to update inquiry status';
                }
            } catch (Exception $e) {
                logActivity("Inquiry status update error: " . $e->getMessage(), 'error');
                $error = 'An error occurred while updating inquiry status';
            }
        }
    } elseif ($action === 'delete' && $inquiry_id) {
        try {
            $result = $db->delete('inquiries', 'id = ?', [$inquiry_id]);
            
            if ($result) {
                $success = 'Inquiry deleted successfully';
                logActivity("Inquiry deleted: ID $inquiry_id");
            } else {
                $error = 'Failed to delete inquiry';
            }
        } catch (Exception $e) {
            logActivity("Inquiry deletion error: " . $e->getMessage(), 'error');
            $error = 'An error occurred while deleting inquiry';
        }
    }
}

// Pagination and filtering
$page = (int)($_GET['page'] ?? 1);
$limit = ITEMS_PER_PAGE;
$offset = ($page - 1) * $limit;
$search = sanitize($_GET['search'] ?? '');
$status_filter = sanitize($_GET['status'] ?? '');
$package_filter = sanitize($_GET['package'] ?? '');

// Build query
$where_conditions = ['1=1'];
$params = [];

if ($search) {
    $where_conditions[] = '(name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?)';
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

if ($status_filter) {
    $where_conditions[] = 'status = ?';
    $params[] = $status_filter;
}

if ($package_filter) {
    $where_conditions[] = 'package_interest = ?';
    $params[] = $package_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count
$total_inquiries = $db->fetch(
    "SELECT COUNT(*) as count FROM inquiries WHERE $where_clause",
    $params
)['count'];

$total_pages = ceil($total_inquiries / $limit);

// Get inquiries
$inquiries = $db->fetchAll(
    "SELECT * FROM inquiries WHERE $where_clause ORDER BY created_at DESC LIMIT ? OFFSET ?",
    array_merge($params, [$limit, $offset])
);

// Get statistics
$stats = [
    'total_inquiries' => $db->fetch('SELECT COUNT(*) as count FROM inquiries')['count'],
    'new_inquiries' => $db->fetch('SELECT COUNT(*) as count FROM inquiries WHERE status = "new"')['count'],
    'replied_inquiries' => $db->fetch('SELECT COUNT(*) as count FROM inquiries WHERE status = "replied"')['count'],
    'closed_inquiries' => $db->fetch('SELECT COUNT(*) as count FROM inquiries WHERE status = "closed"')['count']
];

// Get inquiry for viewing
$view_inquiry = null;
if (isset($_GET['view'])) {
    $view_id = (int)$_GET['view'];
    $view_inquiry = $db->fetch('SELECT * FROM inquiries WHERE id = ?', [$view_id]);
    
    // Mark as read if it's new
    if ($view_inquiry && $view_inquiry['status'] === 'new') {
        $db->update('inquiries', ['status' => 'read'], 'id = ?', [$view_id]);
        $view_inquiry['status'] = 'read';
    }
}
?>

<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1"><?php echo __('manage_inquiries'); ?></h2>
                    <p class="text-muted mb-0">Manage customer inquiries and support requests</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-body text-center">
                    <h3 class="text-primary mb-1"><?php echo $stats['total_inquiries']; ?></h3>
                    <p class="text-muted mb-0">Total Inquiries</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-body text-center">
                    <h3 class="text-danger mb-1"><?php echo $stats['new_inquiries']; ?></h3>
                    <p class="text-muted mb-0">New Inquiries</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-body text-center">
                    <h3 class="text-success mb-1"><?php echo $stats['replied_inquiries']; ?></h3>
                    <p class="text-muted mb-0">Replied</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-body text-center">
                    <h3 class="text-secondary mb-1"><?php echo $stats['closed_inquiries']; ?></h3>
                    <p class="text-muted mb-0">Closed</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Inquiries List -->
        <div class="col-lg-<?php echo $view_inquiry ? '8' : '12'; ?>">
            <!-- Filters and Search -->
            <div class="card shadow-custom border-radius-custom mb-4">
                <div class="card-body">
                    <form method="GET" action="">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="search" class="form-label">Search Inquiries</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="Name, email, subject, or message...">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-control" id="status" name="status">
                                    <option value="">All Statuses</option>
                                    <option value="new" <?php echo $status_filter === 'new' ? 'selected' : ''; ?>>New</option>
                                    <option value="read" <?php echo $status_filter === 'read' ? 'selected' : ''; ?>>Read</option>
                                    <option value="replied" <?php echo $status_filter === 'replied' ? 'selected' : ''; ?>>Replied</option>
                                    <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>Closed</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="package" class="form-label">Package Interest</label>
                                <select class="form-control" id="package" name="package">
                                    <option value="">All Packages</option>
                                    <option value="business" <?php echo $package_filter === 'business' ? 'selected' : ''; ?>>Business</option>
                                    <option value="ecommerce" <?php echo $package_filter === 'ecommerce' ? 'selected' : ''; ?>>E-commerce</option>
                                    <option value="custom" <?php echo $package_filter === 'custom' ? 'selected' : ''; ?>>Custom</option>
                                    <option value="general" <?php echo $package_filter === 'general' ? 'selected' : ''; ?>>General</option>
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>Filter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Inquiries Table -->
            <div class="card shadow-custom border-radius-custom">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-envelope me-2 text-primary"></i>
                        Inquiries (<?php echo $total_inquiries; ?> total)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (empty($inquiries)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-inbox text-muted" style="font-size: 3rem; opacity: 0.3;"></i>
                            <h6 class="text-muted mt-3">No inquiries found</h6>
                            <p class="text-muted">Try adjusting your search criteria.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Subject</th>
                                        <th>Package</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($inquiries as $inquiry): ?>
                                        <tr class="<?php echo $inquiry['status'] === 'new' ? 'table-warning' : ''; ?>">
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($inquiry['name']); ?></strong><br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-envelope me-1"></i>
                                                        <?php echo htmlspecialchars($inquiry['email']); ?>
                                                    </small>
                                                    <?php if ($inquiry['phone']): ?>
                                                        <br><small class="text-muted">
                                                            <i class="fas fa-phone me-1"></i>
                                                            <?php echo htmlspecialchars($inquiry['phone']); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($inquiry['subject']); ?></strong><br>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars(substr($inquiry['message'], 0, 100)); ?>
                                                    <?php if (strlen($inquiry['message']) > 100): ?>...<?php endif; ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php if ($inquiry['package_interest']): ?>
                                                    <span class="badge bg-info">
                                                        <?php echo ucfirst($inquiry['package_interest']); ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = [
                                                    'new' => 'danger',
                                                    'read' => 'warning',
                                                    'replied' => 'success',
                                                    'closed' => 'secondary'
                                                ];
                                                $class = $statusClass[$inquiry['status']] ?? 'secondary';
                                                ?>
                                                <span class="badge bg-<?php echo $class; ?>">
                                                    <?php echo ucfirst($inquiry['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div><?php echo formatDate($inquiry['created_at']); ?></div>
                                                <small class="text-muted"><?php echo timeAgo($inquiry['created_at']); ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="?view=<?php echo $inquiry['id']; ?>" class="btn btn-outline-primary" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="mailto:<?php echo htmlspecialchars($inquiry['email']); ?>?subject=Re: <?php echo urlencode($inquiry['subject']); ?>" 
                                                       class="btn btn-outline-success" title="Reply">
                                                        <i class="fas fa-reply"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-outline-danger" 
                                                            onclick="deleteInquiry(<?php echo $inquiry['id']; ?>)" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                            <div class="mt-4">
                                <?php 
                                $base_url = '/admin/inquiries.php';
                                $query_params = [];
                                if ($search) $query_params['search'] = $search;
                                if ($status_filter) $query_params['status'] = $status_filter;
                                if ($package_filter) $query_params['package'] = $package_filter;
                                
                                if (!empty($query_params)) {
                                    $base_url .= '?' . http_build_query($query_params) . '&';
                                } else {
                                    $base_url .= '?';
                                }
                                
                                echo generatePagination($page, $total_pages, $base_url);
                                ?>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Inquiry Details -->
        <?php if ($view_inquiry): ?>
        <div class="col-lg-4">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-envelope-open me-2 text-primary"></i>
                        Inquiry Details
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>From:</strong><br>
                        <?php echo htmlspecialchars($view_inquiry['name']); ?><br>
                        <small class="text-muted"><?php echo htmlspecialchars($view_inquiry['email']); ?></small>
                        <?php if ($view_inquiry['phone']): ?>
                            <br><small class="text-muted"><?php echo htmlspecialchars($view_inquiry['phone']); ?></small>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Subject:</strong><br>
                        <?php echo htmlspecialchars($view_inquiry['subject']); ?>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Message:</strong><br>
                        <div class="border p-3 rounded bg-light">
                            <?php echo nl2br(htmlspecialchars($view_inquiry['message'])); ?>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Package Interest:</strong><br>
                        <?php if ($view_inquiry['package_interest']): ?>
                            <span class="badge bg-info"><?php echo ucfirst($view_inquiry['package_interest']); ?></span>
                        <?php else: ?>
                            <span class="text-muted">None specified</span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Status:</strong><br>
                        <?php
                        $statusClass = [
                            'new' => 'danger',
                            'read' => 'warning',
                            'replied' => 'success',
                            'closed' => 'secondary'
                        ];
                        $class = $statusClass[$view_inquiry['status']] ?? 'secondary';
                        ?>
                        <span class="badge bg-<?php echo $class; ?>">
                            <?php echo ucfirst($view_inquiry['status']); ?>
                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Received:</strong><br>
                        <?php echo formatDateTime($view_inquiry['created_at']); ?>
                    </div>
                    
                    <?php if ($view_inquiry['admin_notes']): ?>
                    <div class="mb-3">
                        <strong>Admin Notes:</strong><br>
                        <div class="border p-3 rounded bg-warning bg-opacity-10">
                            <?php echo nl2br(htmlspecialchars($view_inquiry['admin_notes'])); ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Update Status Form -->
                    <form method="POST" action="">
                        <input type="hidden" name="action" value="update_status">
                        <input type="hidden" name="inquiry_id" value="<?php echo $view_inquiry['id']; ?>">
                        
                        <div class="mb-3">
                            <label for="status" class="form-label">Update Status:</label>
                            <select class="form-control" id="status" name="status">
                                <option value="new" <?php echo $view_inquiry['status'] === 'new' ? 'selected' : ''; ?>>New</option>
                                <option value="read" <?php echo $view_inquiry['status'] === 'read' ? 'selected' : ''; ?>>Read</option>
                                <option value="replied" <?php echo $view_inquiry['status'] === 'replied' ? 'selected' : ''; ?>>Replied</option>
                                <option value="closed" <?php echo $view_inquiry['status'] === 'closed' ? 'selected' : ''; ?>>Closed</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="admin_notes" class="form-label">Admin Notes:</label>
                            <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3"><?php echo htmlspecialchars($view_inquiry['admin_notes'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update
                            </button>
                            
                            <a href="mailto:<?php echo htmlspecialchars($view_inquiry['email']); ?>?subject=Re: <?php echo urlencode($view_inquiry['subject']); ?>" 
                               class="btn btn-success">
                                <i class="fas fa-reply me-2"></i>Reply via Email
                            </a>
                            
                            <a href="<?php echo SITE_URL; ?>/admin/inquiries.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this inquiry? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="" style="display: inline;">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="inquiry_id" id="deleteInquiryId">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deleteInquiry(id) {
    document.getElementById('deleteInquiryId').value = id;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<?php require_once '../includes/footer.php'; ?>
