<?php
$page_title = 'Place Order';
require_once 'includes/header.php';

// Require login
$auth->requireLogin();

$error = '';
$success = '';

// Get package ID from URL
$package_id = (int)($_GET['package'] ?? 0);

if (!$package_id) {
    redirect('/services.php');
}

// Get package details
$package = $db->fetch('SELECT * FROM service_packages WHERE id = ? AND is_active = 1', [$package_id]);

if (!$package) {
    setErrorMessage('Package not found');
    redirect('/services.php');
}

$user = $auth->getCurrentUser();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $requirements = sanitize($_POST['requirements'] ?? '');
    $notes = sanitize($_POST['notes'] ?? '');
    
    if (empty($requirements)) {
        $error = 'Please provide your requirements';
    } else {
        try {
            $db->beginTransaction();
            
            // Generate order number
            $order_number = generateOrderNumber();
            
            // Create order
            $orderData = [
                'user_id' => $user['id'],
                'package_id' => $package['id'],
                'order_number' => $order_number,
                'total_amount' => $package['price'],
                'currency' => 'INR',
                'status' => 'pending',
                'payment_status' => 'pending',
                'requirements' => $requirements,
                'notes' => $notes
            ];
            
            $order_id = $db->insert('orders', $orderData);
            
            if ($order_id) {
                $db->commit();
                
                // Log activity
                logActivity("New order created: $order_number by {$user['username']}");
                
                // Redirect to payment or success page
                if ($package['price'] > 0) {
                    redirect("/payment.php?order=$order_id");
                } else {
                    setSuccessMessage('Your custom development request has been submitted. We will contact you soon with a quote.');
                    redirect('/dashboard.php');
                }
            } else {
                $db->rollback();
                $error = 'Failed to create order. Please try again.';
            }
        } catch (Exception $e) {
            $db->rollback();
            logActivity("Order creation error: " . $e->getMessage(), 'error');
            $error = 'An error occurred while creating your order.';
        }
    }
}
?>

<div class="container py-5">
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-custom border-radius-custom">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-shopping-cart me-2 text-primary"></i>
                        Place Order
                    </h3>
                </div>
                <div class="card-body p-4">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="">
                        <div class="mb-4">
                            <label for="requirements" class="form-label">Project Requirements *</label>
                            <textarea class="form-control" id="requirements" name="requirements" rows="6" required 
                                      placeholder="Please describe your project requirements in detail..."><?php echo htmlspecialchars($requirements ?? ''); ?></textarea>
                            <small class="text-muted">
                                Please provide as much detail as possible about your project requirements, 
                                preferred colors, content, features, etc.
                            </small>
                        </div>
                        
                        <div class="mb-4">
                            <label for="notes" class="form-label">Additional Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" 
                                      placeholder="Any additional information or special requests..."><?php echo htmlspecialchars($notes ?? ''); ?></textarea>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <?php if ($package['price'] > 0): ?>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-credit-card me-2"></i>Proceed to Payment
                                </button>
                            <?php else: ?>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>Submit Request for Quote
                                </button>
                            <?php endif; ?>
                            
                            <a href="<?php echo SITE_URL; ?>/services.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Services
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Order Summary -->
            <div class="card shadow-custom border-radius-custom mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-receipt me-2 text-primary"></i>
                        Order Summary
                    </h5>
                </div>
                <div class="card-body">
                    <h6 class="fw-bold">
                        <?php echo getCurrentLanguage() === 'hi' ? $package['name_hi'] : $package['name_en']; ?>
                    </h6>
                    <p class="text-muted small mb-3">
                        <?php echo getCurrentLanguage() === 'hi' ? $package['description_hi'] : $package['description_en']; ?>
                    </p>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>Package Price:</span>
                        <span class="fw-bold">
                            <?php if ($package['price'] > 0): ?>
                                <?php echo formatCurrency($package['price']); ?>
                            <?php else: ?>
                                Custom Quote
                            <?php endif; ?>
                        </span>
                    </div>
                    
                    <?php if ($package['price'] > 0): ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax (18% GST):</span>
                            <span><?php echo formatCurrency($package['price'] * 0.18); ?></span>
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between">
                            <span class="fw-bold">Total Amount:</span>
                            <span class="fw-bold text-primary">
                                <?php echo formatCurrency($package['price'] * 1.18); ?>
                            </span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Package Features -->
            <div class="card shadow-custom border-radius-custom">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2 text-primary"></i>
                        Included Features
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="feature-list">
                        <?php 
                        $features = json_decode(getCurrentLanguage() === 'hi' ? $package['features_hi'] : $package['features_en'], true);
                        foreach ($features as $feature): 
                        ?>
                            <li><?php echo $feature; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
            
            <!-- Contact Info -->
            <div class="card shadow-custom border-radius-custom mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-headset me-2 text-primary"></i>
                        Need Help?
                    </h6>
                </div>
                <div class="card-body">
                    <p class="small text-muted mb-3">
                        Have questions about this package? Contact us for assistance.
                    </p>
                    
                    <div class="d-grid gap-2">
                        <a href="https://wa.me/<?php echo str_replace(['+', '-', ' '], '', WHATSAPP_NUMBER); ?>?text=Hi%2C%20I%20have%20questions%20about%20the%20<?php echo urlencode($package['name_en']); ?>%20package" 
                           class="btn btn-success btn-sm" target="_blank">
                            <i class="fab fa-whatsapp me-2"></i>WhatsApp Support
                        </a>
                        
                        <a href="<?php echo SITE_URL; ?>/contact.php?package=<?php echo $package['package_type']; ?>" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-envelope me-2"></i>Send Message
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
