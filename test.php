<?php
/**
 * System Test Page
 * Test various components and configurations
 */

require_once 'includes/header.php';

// Only allow admin access
if (!$auth->isLoggedIn() || $auth->getCurrentUser()['role'] !== 'admin') {
    header('Location: ' . SITE_URL . '/login.php');
    exit;
}

$tests = [];

// Test database connection
try {
    $db_test = $db->fetch('SELECT 1 as test');
    $tests['database'] = [
        'status' => 'success',
        'message' => 'Database connection successful'
    ];
} catch (Exception $e) {
    $tests['database'] = [
        'status' => 'error',
        'message' => 'Database connection failed: ' . $e->getMessage()
    ];
}

// Test email configuration
$tests['email'] = [
    'status' => defined('SMTP_HOST') && SMTP_HOST !== 'smtp.gmail.com' ? 'warning' : 'info',
    'message' => defined('SMTP_HOST') && SMTP_HOST !== 'smtp.gmail.com' ? 'Email configured' : 'Email configuration needs setup'
];

// Test Razorpay configuration
$tests['razorpay'] = [
    'status' => defined('RAZORPAY_KEY_ID') && RAZORPAY_KEY_ID !== 'your-razorpay-key-id' ? 'success' : 'warning',
    'message' => defined('RAZORPAY_KEY_ID') && RAZORPAY_KEY_ID !== 'your-razorpay-key-id' ? 'Razorpay configured' : 'Razorpay needs configuration'
];

// Test reCAPTCHA configuration
$tests['recaptcha'] = [
    'status' => defined('RECAPTCHA_SITE_KEY') && RECAPTCHA_SITE_KEY !== 'your-recaptcha-site-key' ? 'success' : 'warning',
    'message' => defined('RECAPTCHA_SITE_KEY') && RECAPTCHA_SITE_KEY !== 'your-recaptcha-site-key' ? 'reCAPTCHA configured' : 'reCAPTCHA needs configuration'
];

// Test Google Analytics
$tests['analytics'] = [
    'status' => defined('GOOGLE_ANALYTICS_ID') && GOOGLE_ANALYTICS_ID !== 'your-google-analytics-id' ? 'success' : 'info',
    'message' => defined('GOOGLE_ANALYTICS_ID') && GOOGLE_ANALYTICS_ID !== 'your-google-analytics-id' ? 'Google Analytics configured' : 'Google Analytics needs configuration'
];

// Test file permissions
$upload_dir = __DIR__ . '/assets/uploads/';
$tests['permissions'] = [
    'status' => is_writable($upload_dir) ? 'success' : 'error',
    'message' => is_writable($upload_dir) ? 'Upload directory is writable' : 'Upload directory is not writable'
];

// Test PHP version
$tests['php_version'] = [
    'status' => version_compare(PHP_VERSION, '8.0.0', '>=') ? 'success' : 'warning',
    'message' => 'PHP Version: ' . PHP_VERSION
];

// Test required PHP extensions
$required_extensions = ['pdo', 'pdo_mysql', 'curl', 'json', 'openssl', 'mbstring'];
$missing_extensions = [];

foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        $missing_extensions[] = $ext;
    }
}

$tests['php_extensions'] = [
    'status' => empty($missing_extensions) ? 'success' : 'error',
    'message' => empty($missing_extensions) ? 'All required PHP extensions loaded' : 'Missing extensions: ' . implode(', ', $missing_extensions)
];

// Test sample data
$user_count = $db->fetch('SELECT COUNT(*) as count FROM users')['count'];
$package_count = $db->fetch('SELECT COUNT(*) as count FROM service_packages')['count'];

$tests['sample_data'] = [
    'status' => ($user_count > 0 && $package_count > 0) ? 'success' : 'warning',
    'message' => "Users: $user_count, Packages: $package_count"
];

// Test security headers
$tests['security'] = [
    'status' => 'info',
    'message' => 'Security headers are set automatically'
];

// Test language files
$en_exists = file_exists(__DIR__ . '/languages/en.php');
$hi_exists = file_exists(__DIR__ . '/languages/hi.php');

$tests['languages'] = [
    'status' => ($en_exists && $hi_exists) ? 'success' : 'error',
    'message' => "English: " . ($en_exists ? 'OK' : 'Missing') . ", Hindi: " . ($hi_exists ? 'OK' : 'Missing')
];
?>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">System Test Results</h1>
            <p class="text-muted mb-4">This page tests various system components and configurations.</p>
            
            <div class="row">
                <?php foreach ($tests as $test_name => $test): ?>
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">
                                <?php
                                $icon_class = [
                                    'success' => 'fas fa-check-circle text-success',
                                    'warning' => 'fas fa-exclamation-triangle text-warning',
                                    'error' => 'fas fa-times-circle text-danger',
                                    'info' => 'fas fa-info-circle text-info'
                                ];
                                ?>
                                <i class="<?php echo $icon_class[$test['status']]; ?> me-2"></i>
                                <?php echo ucwords(str_replace('_', ' ', $test_name)); ?>
                            </h5>
                            <p class="card-text"><?php echo $test['message']; ?></p>
                            <span class="badge bg-<?php echo $test['status'] === 'success' ? 'success' : ($test['status'] === 'error' ? 'danger' : ($test['status'] === 'warning' ? 'warning' : 'info')); ?>">
                                <?php echo ucfirst($test['status']); ?>
                            </span>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            
            <!-- System Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">System Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>PHP Version:</strong></td>
                                    <td><?php echo PHP_VERSION; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Server Software:</strong></td>
                                    <td><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Document Root:</strong></td>
                                    <td><?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Site URL:</strong></td>
                                    <td><?php echo SITE_URL; ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Memory Limit:</strong></td>
                                    <td><?php echo ini_get('memory_limit'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Max Upload Size:</strong></td>
                                    <td><?php echo ini_get('upload_max_filesize'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Max Execution Time:</strong></td>
                                    <td><?php echo ini_get('max_execution_time'); ?>s</td>
                                </tr>
                                <tr>
                                    <td><strong>Timezone:</strong></td>
                                    <td><?php echo date_default_timezone_get(); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Database Tables -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Database Tables</h5>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        $tables = $db->fetchAll('SHOW TABLES');
                        echo '<div class="row">';
                        foreach ($tables as $table) {
                            $table_name = array_values($table)[0];
                            $count = $db->fetch("SELECT COUNT(*) as count FROM `$table_name`")['count'];
                            echo '<div class="col-md-3 mb-2">';
                            echo '<span class="badge bg-primary me-2">' . $table_name . '</span>';
                            echo '<small class="text-muted">(' . $count . ' records)</small>';
                            echo '</div>';
                        }
                        echo '</div>';
                    } catch (Exception $e) {
                        echo '<p class="text-danger">Error fetching table information: ' . $e->getMessage() . '</p>';
                    }
                    ?>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="<?php echo SITE_URL; ?>/admin/" class="btn btn-primary btn-sm w-100">
                                <i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="<?php echo SITE_URL; ?>/sitemap.php" class="btn btn-info btn-sm w-100" target="_blank">
                                <i class="fas fa-sitemap me-2"></i>View Sitemap
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="<?php echo SITE_URL; ?>/robots.php" class="btn btn-secondary btn-sm w-100" target="_blank">
                                <i class="fas fa-robot me-2"></i>View Robots.txt
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="<?php echo SITE_URL; ?>/" class="btn btn-success btn-sm w-100">
                                <i class="fas fa-home me-2"></i>View Website
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Configuration Status -->
            <div class="alert alert-info mt-4">
                <h6><i class="fas fa-info-circle me-2"></i>Configuration Notes</h6>
                <ul class="mb-0">
                    <li>Update API keys in <code>config/config.php</code> for production use</li>
                    <li>Configure SMTP settings for email functionality</li>
                    <li>Set up SSL certificate for secure connections</li>
                    <li>Enable caching and optimization for production</li>
                    <li>Regular backup of database and files is recommended</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
