# WebsiteDeveloper0002.in

A complete web development services website built with PHP, MySQL, HTML, CSS, and JavaScript. Features bilingual support (English/Hindi), payment integration with Razorpay, and comprehensive admin panel.

## Features

### Core Features
- **Bilingual Support**: English and Hindi language toggle
- **Responsive Design**: Mobile-first approach with Bootstrap 5
- **User Authentication**: Secure login/register with role-based access
- **Service Packages**: Three main packages (Business, E-commerce, Custom)
- **Payment Integration**: Razorpay payment gateway
- **Admin Dashboard**: Comprehensive management panel
- **Email System**: Automated notifications and newsletters
- **Security Features**: reCAPTCHA, CSRF protection, input validation
- **SEO Optimized**: Meta tags, structured data, sitemap generation
- **Analytics**: Google Analytics integration and custom event tracking

### Service Packages
1. **Business Premium Package** - ₹15,000
   - Free Domain & Hosting Setup
   - Multi-page website with secure authentication
   - Query Dashboard
   - Essential on-page SEO

2. **E-commerce Package** - ₹75,000
   - Complete online store setup
   - Payment gateway integration
   - Inventory management
   - Order tracking system

3. **Custom Development** - Price on Request
   - Custom solutions (CRM, booking systems, LMS)
   - Modular pricing
   - Google Analytics integration
   - Requirement-based scoping

## Technology Stack

- **Backend**: PHP 8.0+, MySQL 8.0+
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Framework**: Bootstrap 5.3
- **Icons**: Font Awesome 6
- **Payment**: Razorpay API
- **Email**: PHP Mail (upgradeable to PHPMailer)
- **Security**: Google reCAPTCHA v2

## Installation

### Prerequisites
- PHP 8.0 or higher
- MySQL 8.0 or higher
- Apache/Nginx web server
- Composer (optional, for PHPMailer)

### Setup Steps

1. **Clone/Download the project**
   ```bash
   git clone <repository-url>
   cd websitedeveloper0002.in
   ```

2. **Database Setup**
   ```bash
   # Import the database schema
   mysql -u root -p < database/schema.sql
   
   # Import sample data (optional)
   mysql -u root -p < database/sample_data.sql
   ```

3. **Configuration**
   - Copy `config/config.php` and update the following:
     - Database credentials
     - Razorpay API keys
     - Google reCAPTCHA keys
     - Google Analytics ID
     - SMTP settings
     - Site URL and other constants

4. **File Permissions**
   ```bash
   chmod 755 assets/uploads/
   chmod 644 config/config.php
   ```

5. **Web Server Configuration**
   - Point document root to the project directory
   - Enable mod_rewrite (for Apache)
   - Configure SSL certificate (recommended)

## Configuration

### Required API Keys

1. **Razorpay**
   - Sign up at https://razorpay.com
   - Get Key ID and Key Secret
   - Update in `config/config.php`

2. **Google reCAPTCHA**
   - Get keys from https://www.google.com/recaptcha
   - Update site key and secret key in config

3. **Google Analytics**
   - Create property at https://analytics.google.com
   - Update tracking ID in config

### Email Configuration
- Configure SMTP settings for email notifications
- For production, consider using PHPMailer with proper SMTP

## File Structure

```
websitedeveloper0002.in/
├── admin/                  # Admin panel files
│   ├── index.php          # Admin dashboard
│   ├── packages.php       # Package management
│   ├── users.php          # User management
│   └── inquiries.php      # Inquiry management
├── assets/                # Static assets
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   ├── images/            # Images
│   └── uploads/           # User uploads
├── config/                # Configuration files
│   ├── config.php         # Main configuration
│   └── database.php       # Database class
├── database/              # Database files
│   ├── schema.sql         # Database structure
│   └── sample_data.sql    # Sample data
├── includes/              # PHP includes
│   ├── header.php         # Common header
│   ├── footer.php         # Common footer
│   ├── auth.php           # Authentication
│   ├── functions.php      # Utility functions
│   ├── payment.php        # Payment functions
│   ├── email.php          # Email functions
│   ├── security.php       # Security functions
│   └── seo.php            # SEO functions
├── languages/             # Language files
│   ├── en.php             # English translations
│   └── hi.php             # Hindi translations
├── index.php              # Homepage
├── services.php           # Services page
├── contact.php            # Contact page
├── login.php              # Login page
├── register.php           # Registration page
├── dashboard.php          # User dashboard
├── orders.php             # Order history
├── profile.php            # User profile
├── payment.php            # Payment page
├── sitemap.php            # Dynamic sitemap
└── robots.php             # Dynamic robots.txt
```

## Usage

### Admin Access
1. Create admin user in database or register and manually change role to 'admin'
2. Access admin panel at `/admin/`
3. Manage packages, users, orders, and inquiries

### Customer Flow
1. Browse services on homepage
2. Register/Login
3. Place order for desired package
4. Make payment via Razorpay
5. Track order status in dashboard

### Package Management
- Add/edit service packages via admin panel
- Set pricing, features, and descriptions
- Enable/disable packages
- View order statistics

## Security Features

- **Input Validation**: All user inputs are sanitized
- **CSRF Protection**: Forms protected against CSRF attacks
- **SQL Injection Prevention**: Prepared statements used
- **XSS Protection**: Output escaped and validated
- **Rate Limiting**: Prevents brute force attacks
- **IP Blacklisting**: Block malicious IPs
- **Security Headers**: Proper HTTP security headers
- **Password Hashing**: Secure password storage

## SEO Features

- **Meta Tags**: Dynamic meta tags for each page
- **Structured Data**: JSON-LD markup for better search visibility
- **Sitemap**: Auto-generated XML sitemap
- **Robots.txt**: Dynamic robots.txt generation
- **Open Graph**: Social media sharing optimization
- **Page Speed**: Optimized loading with lazy loading
- **Mobile-First**: Responsive design for all devices

## Testing

### Manual Testing Checklist

1. **User Registration/Login**
   - [ ] Register new user
   - [ ] Login with valid credentials
   - [ ] Password reset functionality
   - [ ] Role-based access control

2. **Service Packages**
   - [ ] View all packages
   - [ ] Package details display correctly
   - [ ] Pricing and features accurate

3. **Order Process**
   - [ ] Place order for each package type
   - [ ] Payment integration works
   - [ ] Order confirmation emails sent
   - [ ] Order status updates

4. **Admin Panel**
   - [ ] Admin login
   - [ ] Manage packages
   - [ ] View orders and users
   - [ ] Handle inquiries

5. **Contact Forms**
   - [ ] Contact form submission
   - [ ] reCAPTCHA validation
   - [ ] Email notifications

6. **Language Toggle**
   - [ ] Switch between English/Hindi
   - [ ] Content displays correctly
   - [ ] Forms work in both languages

### Performance Testing
- Test page load speeds
- Check mobile responsiveness
- Validate HTML/CSS
- Test with different browsers

## Deployment

### Production Checklist

1. **Security**
   - [ ] Change default passwords
   - [ ] Update all API keys
   - [ ] Enable HTTPS
   - [ ] Set proper file permissions
   - [ ] Disable error reporting

2. **Configuration**
   - [ ] Update site URL
   - [ ] Configure email settings
   - [ ] Set up backup system
   - [ ] Configure monitoring

3. **Performance**
   - [ ] Enable caching
   - [ ] Optimize images
   - [ ] Minify CSS/JS
   - [ ] Set up CDN (optional)

4. **SEO**
   - [ ] Submit sitemap to search engines
   - [ ] Set up Google Analytics
   - [ ] Configure Google Search Console
   - [ ] Test structured data

## Support

For support and customization:
- Email: <EMAIL>
- Phone: +91-**********
- Website: https://websitedeveloper0002.in

## License

This project is proprietary software developed for WebsiteDeveloper0002.in.

## Changelog

### Version 1.0.0 (2025-09-13)
- Initial release
- Complete website with all features
- Admin panel implementation
- Payment integration
- Email system
- Security features
- SEO optimization
