/**
 * Main JavaScript file for WebsiteDeveloper0002.in
 */

// Global variables
let currentLanguage = 'en';

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeComponents();
    setupEventListeners();
    loadLanguagePreference();
});

// Initialize all components
function initializeComponents() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Initialize smooth scrolling
    initializeSmoothScrolling();
    
    // Initialize form validation
    initializeFormValidation();
    
    // Initialize loading states
    initializeLoadingStates();
}

// Setup event listeners
function setupEventListeners() {
    // Language switcher
    document.addEventListener('click', function(e) {
        if (e.target.matches('a[href*="lang="]')) {
            e.preventDefault();
            const lang = e.target.href.split('lang=')[1];
            switchLanguage(lang);
        }
    });
    
    // Form submissions
    document.addEventListener('submit', function(e) {
        if (e.target.matches('form')) {
            handleFormSubmission(e);
        }
    });
    
    // Button clicks
    document.addEventListener('click', function(e) {
        if (e.target.matches('.btn-primary, .btn-success, .btn-outline-primary')) {
            trackButtonClick(e.target);
        }
    });
    
    // Scroll events
    window.addEventListener('scroll', handleScroll);
    
    // Resize events
    window.addEventListener('resize', handleResize);
}

// Smooth scrolling for anchor links
function initializeSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Form validation
function initializeFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
}

// Loading states for buttons
function initializeLoadingStates() {
    document.addEventListener('click', function(e) {
        if (e.target.matches('button[type="submit"], .btn-submit')) {
            showButtonLoading(e.target);
        }
    });
}

// Language switching
function switchLanguage(lang) {
    if (['en', 'hi'].includes(lang)) {
        currentLanguage = lang;
        localStorage.setItem('preferred_language', lang);
        
        // Update URL
        const url = new URL(window.location);
        url.searchParams.set('lang', lang);
        window.location.href = url.toString();
    }
}

// Load language preference
function loadLanguagePreference() {
    const savedLang = localStorage.getItem('preferred_language');
    if (savedLang && savedLang !== currentLanguage) {
        switchLanguage(savedLang);
    }
}

// Handle form submissions
function handleFormSubmission(e) {
    const form = e.target;
    const submitBtn = form.querySelector('button[type="submit"]');
    
    if (submitBtn) {
        showButtonLoading(submitBtn);
        
        // Re-enable button after 5 seconds (fallback)
        setTimeout(() => {
            hideButtonLoading(submitBtn);
        }, 5000);
    }
}

// Show loading state on button
function showButtonLoading(button) {
    const originalText = button.innerHTML;
    button.setAttribute('data-original-text', originalText);
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
    button.disabled = true;
}

// Hide loading state on button
function hideButtonLoading(button) {
    const originalText = button.getAttribute('data-original-text');
    if (originalText) {
        button.innerHTML = originalText;
        button.removeAttribute('data-original-text');
    }
    button.disabled = false;
}

// Handle scroll events
function handleScroll() {
    const scrollTop = window.pageYOffset;
    
    // Show/hide back to top button
    const backToTop = document.querySelector('.back-to-top');
    if (backToTop) {
        if (scrollTop > 300) {
            backToTop.style.display = 'block';
        } else {
            backToTop.style.display = 'none';
        }
    }
    
    // Navbar background on scroll
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        if (scrollTop > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }
    
    // Animate elements on scroll
    animateOnScroll();
}

// Handle resize events
function handleResize() {
    // Adjust mobile menu
    const navbarCollapse = document.querySelector('.navbar-collapse');
    if (window.innerWidth > 992 && navbarCollapse) {
        navbarCollapse.classList.remove('show');
    }
}

// Animate elements when they come into view
function animateOnScroll() {
    const elements = document.querySelectorAll('.animate-on-scroll');
    
    elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;
        
        if (elementTop < window.innerHeight - elementVisible) {
            element.classList.add('animated');
        }
    });
}

// Track button clicks for analytics
function trackButtonClick(button) {
    const buttonText = button.textContent.trim();
    const buttonClass = button.className;
    
    // Google Analytics event
    if (typeof gtag !== 'undefined') {
        gtag('event', 'click', {
            'event_category': 'button',
            'event_label': buttonText,
            'event_value': 1
        });
    }
    
    // Console log for debugging
    console.log('Button clicked:', buttonText, buttonClass);
}

// Utility functions
const utils = {
    // Format currency
    formatCurrency: function(amount, currency = 'INR') {
        const formatter = new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 0
        });
        return formatter.format(amount);
    },
    
    // Format date
    formatDate: function(date, locale = 'en-IN') {
        return new Date(date).toLocaleDateString(locale);
    },
    
    // Debounce function
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Show notification
    showNotification: function(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    },
    
    // Copy to clipboard
    copyToClipboard: function(text) {
        navigator.clipboard.writeText(text).then(() => {
            this.showNotification('Copied to clipboard!', 'success');
        }).catch(() => {
            this.showNotification('Failed to copy to clipboard', 'danger');
        });
    },
    
    // Validate email
    isValidEmail: function(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },
    
    // Validate phone (Indian format)
    isValidPhone: function(phone) {
        const phoneRegex = /^(\+91|91|0)?[6789]\d{9}$/;
        return phoneRegex.test(phone.replace(/[\s-]/g, ''));
    }
};

// Export utils for global access
window.utils = utils;

// Service Worker registration (for PWA features)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registration successful');
            })
            .catch(function(err) {
                console.log('ServiceWorker registration failed');
            });
    });
}

// Performance monitoring
window.addEventListener('load', function() {
    // Log page load time
    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
    console.log('Page load time:', loadTime + 'ms');
    
    // Send to analytics if available
    if (typeof gtag !== 'undefined') {
        gtag('event', 'timing_complete', {
            'name': 'load',
            'value': loadTime
        });
    }
});

// Error handling
window.addEventListener('error', function(e) {
    console.error('JavaScript error:', e.error);
    
    // Send to analytics if available
    if (typeof gtag !== 'undefined') {
        gtag('event', 'exception', {
            'description': e.error.toString(),
            'fatal': false
        });
    }
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + K for search (if search functionality exists)
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.querySelector('input[type="search"], #search');
        if (searchInput) {
            searchInput.focus();
        }
    }
    
    // Escape to close modals
    if (e.key === 'Escape') {
        const openModal = document.querySelector('.modal.show');
        if (openModal) {
            const modal = bootstrap.Modal.getInstance(openModal);
            if (modal) {
                modal.hide();
            }
        }
    }
});

// Print styles
window.addEventListener('beforeprint', function() {
    document.body.classList.add('printing');
});

window.addEventListener('afterprint', function() {
    document.body.classList.remove('printing');
});

console.log('WebsiteDeveloper0002.in - Main JS loaded successfully');
